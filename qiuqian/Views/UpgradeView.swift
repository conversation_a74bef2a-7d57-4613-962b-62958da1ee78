//
//  UpgradeView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct UpgradeView: View {
    @EnvironmentObject var userState: UserState
    @Environment(\.dismiss) private var dismiss
    @State private var selectedPlan: PricingPlan = .monthly
    
    enum PricingPlan: CaseIterable {
        case monthly
        case yearly
        
        var title: String {
            switch self {
            case .monthly:
                return "月度会员"
            case .yearly:
                return "年度会员"
            }
        }
        
        var price: String {
            switch self {
            case .monthly:
                return "¥29"
            case .yearly:
                return "¥299"
            }
        }
        
        var period: String {
            switch self {
            case .monthly:
                return "/月"
            case .yearly:
                return "/年"
            }
        }
        
        var savings: String? {
            switch self {
            case .monthly:
                return nil
            case .yearly:
                return "节省 ¥49"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 头部介绍
                    headerSection
                    
                    // 功能对比
                    featuresSection
                    
                    // 价格选择
                    pricingSection
                    
                    // 升级按钮
                    upgradeButton
                    
                    // 说明文字
                    disclaimerSection
                }
                .padding()
            }
            .navigationTitle("升级会员")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 头部介绍
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "crown.fill")
                .font(.system(size: 60))
                .foregroundColor(.yellow)
            
            Text("解锁全部功能")
                .font(.title)
                .fontWeight(.bold)
            
            Text("升级到付费会员，享受完整的AI条款分析体验")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - 功能对比
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("会员特权")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                featureRow(
                    icon: "doc.badge.plus",
                    title: "无限文件上传",
                    description: "支持PDF、Word、图片等多种格式",
                    isPremium: true
                )
                
                featureRow(
                    icon: "brain.head.profile",
                    title: "AI智能分析",
                    description: "深度分析条款风险，生成详细报告",
                    isPremium: true
                )
                
                featureRow(
                    icon: "bubble.left.and.bubble.right",
                    title: "AI追问对话",
                    description: "与AI深入讨论条款细节和风险",
                    isPremium: true
                )
                
                featureRow(
                    icon: "doc.text",
                    title: "导出分析报告",
                    description: "生成专业PDF报告，便于保存分享",
                    isPremium: true
                )
                
                featureRow(
                    icon: "eye",
                    title: "浏览精选条款",
                    description: "查看来自热门应用的条款解析",
                    isPremium: false
                )
                
                featureRow(
                    icon: "heart",
                    title: "收藏条款",
                    description: "收藏重要条款，随时查看",
                    isPremium: false
                )
            }
        }
    }
    
    // MARK: - 价格选择
    private var pricingSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("选择套餐")
                .font(.headline)
            
            VStack(spacing: 12) {
                ForEach(PricingPlan.allCases, id: \.self) { plan in
                    pricingCard(for: plan)
                }
            }
        }
    }
    
    // MARK: - 升级按钮
    private var upgradeButton: some View {
        Button(action: {
            // 处理购买逻辑
            userState.upgradeToPremium()
            dismiss()
        }) {
            Text("立即升级 - \(selectedPlan.price)\(selectedPlan.period)")
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
        }
    }
    
    // MARK: - 说明文字
    private var disclaimerSection: some View {
        VStack(spacing: 8) {
            Text("• 订阅将在当前订阅期结束前24小时自动续费")
            Text("• 可随时在设置中取消自动续费")
            Text("• 升级后立即生效，享受所有会员功能")
        }
        .font(.caption)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.leading)
    }
    
    // MARK: - 辅助方法
    private func featureRow(icon: String, title: String, description: String, isPremium: Bool) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(isPremium ? .blue : .green)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 2) {
                HStack {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    if isPremium {
                        Text("会员")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.2))
                            .foregroundColor(.blue)
                            .cornerRadius(4)
                    } else {
                        Text("免费")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.green.opacity(0.2))
                            .foregroundColor(.green)
                            .cornerRadius(4)
                    }
                }
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    private func pricingCard(for plan: PricingPlan) -> some View {
        Button(action: {
            selectedPlan = plan
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(plan.title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    HStack(alignment: .bottom, spacing: 4) {
                        Text(plan.price)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                        
                        Text(plan.period)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        if let savings = plan.savings {
                            Text(savings)
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.orange.opacity(0.2))
                                .foregroundColor(.orange)
                                .cornerRadius(4)
                        }
                    }
                }
                
                Spacer()
                
                Image(systemName: selectedPlan == plan ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(selectedPlan == plan ? .blue : .gray)
                    .font(.title2)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(selectedPlan == plan ? Color.blue : Color.gray.opacity(0.3), lineWidth: selectedPlan == plan ? 2 : 1)
                    .background(selectedPlan == plan ? Color.blue.opacity(0.05) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    UpgradeView()
        .environmentObject(UserState())
}
