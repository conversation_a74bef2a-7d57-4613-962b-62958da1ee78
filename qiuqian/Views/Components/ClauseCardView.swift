//
//  ClauseCardView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct ClauseCardView: View {
    let clause: TermsClause
    @EnvironmentObject var userState: UserState
    
    var body: some View {
        HStack(spacing: Theme.Layout.paddingL) {
            // 左侧应用图标
            AppIconView(appName: clause.appName)

            // 右侧内容
            VStack(alignment: .leading, spacing: Theme.Layout.paddingS) {
                // 条款标题
                Text(clause.title)
                    .font(Theme.Typography.headline)
                    .foregroundColor(Theme.textPrimary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                // 应用信息和风险等级
                HStack(spacing: Theme.Layout.paddingS) {
                    // 应用名称
                    Text(clause.appName)
                        .font(Theme.Typography.subheadline)
                        .foregroundColor(Theme.textSecondary)

                    Text("•")
                        .font(Theme.Typography.caption)
                        .foregroundColor(Theme.textSecondary)

                    // 风险等级
                    RiskLevelBadge(clause.riskLevel)

                    Spacer()
                }

                // AI摘要
                Text(clause.aiSummary)
                    .font(Theme.Typography.subheadline)
                    .foregroundColor(Theme.textSecondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }

            Spacer()
        }
        .listItemStyle()
    }

}

#Preview {
    VStack(spacing: 0) {
        ClauseCardView(clause: TermsClause(
            title: "用户数据收集条款",
            content: "我们可能会收集您的个人信息，包括但不限于姓名、邮箱地址、设备信息等，用于改善服务质量。",
            summary: "应用会收集用户个人信息用于服务改善",
            riskLevel: .medium,
            category: .privacy,
            appName: "微信",
            appCategory: "社交",
            aiAnalysis: "该条款允许应用收集广泛的个人信息，建议用户仔细考虑隐私风险。",
            aiSummary: "中等隐私风险，需注意个人信息保护",
            keywords: ["个人信息", "数据收集", "隐私"],
            createdAt: Date(),
            updatedAt: Date()
        ))
        .environmentObject(UserState())
        
        Divider()
            .padding(.leading, 20)
        
        ClauseCardView(clause: TermsClause(
            title: "服务免责声明",
            content: "本公司对因使用本服务而导致的任何直接或间接损失不承担责任。",
            summary: "公司对使用服务造成的损失不承担责任",
            riskLevel: .high,
            category: .liability,
            appName: "支付宝",
            appCategory: "金融",
            aiAnalysis: "该免责条款过于宽泛，几乎免除了公司的所有责任。",
            aiSummary: "高风险免责条款，用户权益保护不足",
            keywords: ["免责", "损失", "责任"],
            createdAt: Date(),
            updatedAt: Date()
        ))
        .environmentObject(UserState())
    }
}
