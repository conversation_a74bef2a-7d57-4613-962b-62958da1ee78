//
//  AnalysisResultView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct AnalysisResultView: View {
    let analysisResult: TermsAnalysisResult
    @EnvironmentObject var userState: UserState
    @State private var selectedRiskFilter: RiskLevel?
    @State private var showingExportSheet = false
    @State private var showingUpgradeSheet = false
    
    var filteredClauses: [TermsClause] {
        guard let riskLevel = selectedRiskFilter else {
            return analysisResult.clauses
        }
        return analysisResult.clauses.filter { $0.riskLevel == riskLevel }
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 文件信息
                fileInfoSection
                
                // 风险总览
                riskOverviewSection
                
                // 风险筛选
                riskFilterSection
                
                // 条款列表
                clausesListSection
                
                // 导出报告按钮
                exportSection
            }
            .padding()
        }
        .navigationTitle("分析结果")
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $showingExportSheet) {
            ExportReportView(analysisResult: analysisResult)
                .environmentObject(userState)
        }
        .sheet(isPresented: $showingUpgradeSheet) {
            UpgradeView()
                .environmentObject(userState)
        }
    }
    
    // MARK: - 文件信息区域
    private var fileInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "doc.text.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(analysisResult.fileName)
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    HStack {
                        Text("上传时间：\(analysisResult.uploadDate, style: .date)")
                        Text("•")
                        Text("文件大小：\(ByteCountFormatter.string(fromByteCount: Int64(analysisResult.fileSize), countStyle: .file))")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding()
            .background(Color.blue.opacity(0.05))
            .cornerRadius(12)
        }
    }
    
    // MARK: - 风险总览区域
    private var riskOverviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("风险总览")
                .font(.headline)
            
            // 总体风险评分
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("总体风险评分")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    HStack(alignment: .bottom, spacing: 4) {
                        Text("\(Int(analysisResult.overallRiskScore * 100))")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(riskScoreColor)
                        
                        Text("/ 100")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 风险等级分布
                VStack(alignment: .trailing, spacing: 8) {
                    riskCountRow(.high, count: analysisResult.highRiskCount)
                    riskCountRow(.medium, count: analysisResult.mediumRiskCount)
                    riskCountRow(.low, count: analysisResult.lowRiskCount)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
            
            // AI总结
            VStack(alignment: .leading, spacing: 8) {
                Text("AI分析摘要")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(analysisResult.aiSummary)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.blue.opacity(0.05))
            .cornerRadius(12)
        }
    }
    
    // MARK: - 风险筛选区域
    private var riskFilterSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("条款筛选")
                .font(.headline)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // 全部条款
                    filterButton(title: "全部条款", count: analysisResult.totalClauses, isSelected: selectedRiskFilter == nil) {
                        selectedRiskFilter = nil
                    }
                    
                    // 各风险等级
                    ForEach(RiskLevel.allCases) { level in
                        let count = analysisResult.riskDistribution[level] ?? 0
                        filterButton(
                            title: level.displayName,
                            count: count,
                            color: level.color,
                            isSelected: selectedRiskFilter == level
                        ) {
                            selectedRiskFilter = level
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - 条款列表区域
    private var clausesListSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("条款详情")
                    .font(.headline)
                
                Spacer()
                
                Text("\(filteredClauses.count) 个条款")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            LazyVStack(spacing: 12) {
                ForEach(filteredClauses) { clause in
                    NavigationLink(destination: ClauseDetailView(clause: clause)) {
                        ClauseRowView(clause: clause)
                            .environmentObject(userState)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    // MARK: - 导出区域
    private var exportSection: some View {
        VStack(spacing: 12) {
            if userState.canExportReports {
                Button(action: {
                    showingExportSheet = true
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("导出分析报告")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
            } else {
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.gray)
                        Text("导出报告功能仅限付费用户")
                            .foregroundColor(.gray)
                    }
                    
                    Button("升级会员解锁") {
                        showingUpgradeSheet = true
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
    
    // MARK: - 辅助方法
    private var riskScoreColor: Color {
        let score = analysisResult.overallRiskScore
        if score >= 0.7 {
            return .red
        } else if score >= 0.4 {
            return .orange
        } else {
            return .green
        }
    }
    
    private func riskCountRow(_ level: RiskLevel, count: Int) -> some View {
        HStack(spacing: 8) {
            Image(systemName: level.icon)
                .font(.caption)
                .foregroundColor(level.color)
            
            Text(level.displayName)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(count)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(level.color)
        }
    }
    
    private func filterButton(
        title: String,
        count: Int,
        color: Color = .blue,
        isSelected: Bool,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("(\(count))")
                    .font(.caption)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isSelected ? color.opacity(0.2) : Color.gray.opacity(0.1))
            .foregroundColor(isSelected ? color : .primary)
            .cornerRadius(8)
        }
    }
}

#Preview {
    NavigationView {
        AnalysisResultView(analysisResult: TermsAnalysisResult(
            fileName: "微信用户协议.pdf",
            fileSize: 2048000,
            uploadDate: Date(),
            totalClauses: 15,
            highRiskCount: 3,
            mediumRiskCount: 7,
            lowRiskCount: 5,
            clauses: MockDataService.shared.featuredClauses,
            overallRiskScore: 0.65,
            aiSummary: "该协议包含多项需要注意的条款，特别是在数据收集和免责声明方面存在一定风险。建议用户仔细阅读相关条款并谨慎使用相关功能。"
        ))
        .environmentObject(UserState())
    }
}
