//
//  HomeView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct HomeView: View {
    @EnvironmentObject var userState: UserState
    @EnvironmentObject var dataService: MockDataService
    @State private var searchText = ""
    @State private var selectedRiskLevel: RiskLevel?
    @State private var selectedCategory: ClauseCategory?
    @State private var showingUpgradeSheet = false

    var filteredClauses: [TermsClause] {
        var clauses = dataService.featuredClauses

        // 搜索过滤
        if !searchText.isEmpty {
            clauses = dataService.searchClauses(keyword: searchText)
        }

        // 风险等级过滤
        if let riskLevel = selectedRiskLevel {
            clauses = clauses.filter { $0.riskLevel == riskLevel }
        }

        // 分类过滤
        if let category = selectedCategory {
            clauses = clauses.filter { $0.category == category }
        }

        return clauses
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部标题和搜索
                headerSection

                // 筛选标签
                filterTabsSection

                // 条款列表
                clausesList
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingUpgradeSheet) {
            UpgradeView()
                .environmentObject(userState)
        }
    }
    
    // MARK: - 顶部标题和搜索区域
    private var headerSection: some View {
        VStack(spacing: Theme.Layout.paddingL) {
            // 状态栏占位
            Rectangle()
                .fill(Color.clear)
                .frame(height: 44)

            // 标题和搜索按钮
            HStack {
                Text("条款解析")
                    .font(Theme.Typography.largeTitle)
                    .foregroundColor(Theme.textPrimary)

                Spacer()

                Button(action: {
                    // 搜索功能
                }) {
                    Image(systemName: "magnifyingglass")
                        .font(Theme.Typography.title2)
                        .foregroundColor(Theme.textPrimary)
                }
            }
            .padding(.horizontal, Theme.Layout.paddingXL)
        }
        .background(Theme.backgroundPrimary)
    }
    
    // MARK: - 筛选标签区域
    private var filterTabsSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: Theme.Layout.paddingL) {
                // 全部
                filterTab(title: "全部", isSelected: selectedCategory == nil && selectedRiskLevel == nil) {
                    selectedCategory = nil
                    selectedRiskLevel = nil
                }

                // 风险等级筛选
                ForEach(RiskLevel.allCases) { level in
                    filterTab(
                        title: level.displayName,
                        isSelected: selectedRiskLevel == level
                    ) {
                        selectedRiskLevel = selectedRiskLevel == level ? nil : level
                        selectedCategory = nil
                    }
                }

                // 分类筛选
                ForEach(ClauseCategory.allCases) { category in
                    filterTab(
                        title: category.displayName,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = selectedCategory == category ? nil : category
                        selectedRiskLevel = nil
                    }
                }
            }
            .padding(.horizontal, Theme.Layout.paddingXL)
        }
        .padding(.vertical, Theme.Layout.paddingM)
    }
    
    // MARK: - 条款列表
    private var clausesList: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(filteredClauses) { clause in
                    NavigationLink(destination: ClauseDetailView(clause: clause)) {
                        ClauseCardView(clause: clause)
                            .environmentObject(userState)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 分隔线
                    if clause.id != filteredClauses.last?.id {
                        CustomDivider(indent: Theme.Layout.dividerIndent)
                    }
                }

                // 底部间距
                Rectangle()
                    .fill(Color.clear)
                    .frame(height: 100)
            }
        }
    }

    // MARK: - 筛选标签
    private func filterTab(title: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(title)
                .font(Theme.Typography.subheadline)
                .fontWeight(isSelected ? .semibold : .regular)
                .foregroundColor(isSelected ? Theme.textPrimary : Theme.textSecondary)
                .padding(.bottom, Theme.Layout.paddingS)
                .overlay(
                    Rectangle()
                        .frame(height: 2)
                        .foregroundColor(isSelected ? Theme.textPrimary : .clear),
                    alignment: .bottom
                )
        }
    }
}

#Preview {
    HomeView()
        .environmentObject(UserState())
        .environmentObject(MockDataService.shared)
}
