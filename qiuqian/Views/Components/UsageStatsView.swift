//
//  UsageStatsView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct UsageStatsView: View {
    @EnvironmentObject var userState: UserState
    
    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Layout.paddingL) {
            Text("使用统计")
                .font(Theme.Typography.headline)
                .foregroundColor(Theme.textPrimary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: Theme.Layout.paddingL) {
                // 上传文件数
                StatCard(
                    icon: "doc.badge.plus",
                    title: "已分析文件",
                    value: "\(userState.uploadHistory.count)",
                    subtitle: "个文件",
                    color: Theme.primaryBlue
                )

                // 收藏条款数
                StatCard(
                    icon: "heart.fill",
                    title: "收藏条款",
                    value: "\(userState.favoriteClauseIds.count)",
                    subtitle: "个条款",
                    color: Theme.error
                )

                // 会员状态
                StatCard(
                    icon: userState.isPremiumUser ? "crown.fill" : "person.fill",
                    title: "会员状态",
                    value: userState.subscriptionStatus.displayName,
                    subtitle: userState.isTrialUser ? "剩余 \(userState.remainingTrialUsage) 次" : "",
                    color: userState.isPremiumUser ? Theme.premium : Theme.free
                )

                // 风险发现
                StatCard(
                    icon: "exclamationmark.triangle.fill",
                    title: "风险发现",
                    value: "\(totalRisksFound)",
                    subtitle: "个风险点",
                    color: Theme.warning
                )
            }
        }
    }
    
    private var totalRisksFound: Int {
        userState.uploadHistory.reduce(0) { total, result in
            total + result.highRiskCount + result.mediumRiskCount
        }
    }
    

}

#Preview {
    UsageStatsView()
        .environmentObject(UserState())
        .padding()
}
