//
//  ExportReportView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct ExportReportView: View {
    let analysisResult: TermsAnalysisResult
    @EnvironmentObject var userState: UserState
    @Environment(\.dismiss) private var dismiss
    @State private var selectedFormat: ExportFormat = .pdf
    @State private var includeFullText = true
    @State private var includeAIAnalysis = true
    @State private var includeRiskSummary = true
    @State private var isExporting = false
    @State private var exportProgress: Double = 0.0
    
    enum ExportFormat: String, CaseIterable {
        case pdf = "PDF"
        case word = "Word"
        
        var icon: String {
            switch self {
            case .pdf:
                return "doc.richtext"
            case .word:
                return "doc.text"
            }
        }
        
        var description: String {
            switch self {
            case .pdf:
                return "适合阅读和分享"
            case .word:
                return "适合编辑和修改"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 导出格式选择
                    formatSelectionSection
                    
                    // 内容选项
                    contentOptionsSection
                    
                    // 预览信息
                    previewSection
                    
                    // 导出按钮
                    if !isExporting {
                        exportButton
                    } else {
                        exportProgressView
                    }
                }
                .padding()
            }
            .navigationTitle("导出报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 格式选择区域
    private var formatSelectionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("选择导出格式")
                .font(.headline)
            
            VStack(spacing: 12) {
                ForEach(ExportFormat.allCases, id: \.self) { format in
                    formatCard(for: format)
                }
            }
        }
    }
    
    // MARK: - 内容选项区域
    private var contentOptionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("报告内容")
                .font(.headline)
            
            VStack(spacing: 16) {
                optionToggle(
                    title: "包含条款原文",
                    description: "完整的条款文本内容",
                    isOn: $includeFullText
                )
                
                optionToggle(
                    title: "包含AI分析",
                    description: "详细的AI风险分析和建议",
                    isOn: $includeAIAnalysis
                )
                
                optionToggle(
                    title: "包含风险摘要",
                    description: "风险等级统计和总体评估",
                    isOn: $includeRiskSummary
                )
            }
        }
    }
    
    // MARK: - 预览区域
    private var previewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("报告预览")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: selectedFormat.icon)
                        .foregroundColor(.blue)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("\(analysisResult.fileName)_分析报告.\(selectedFormat.rawValue.lowercased())")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("预计大小：\(estimatedFileSize)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                
                Divider()
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("报告将包含：")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        if includeRiskSummary {
                            reportContentRow("风险总览和评分")
                        }
                        
                        reportContentRow("\(analysisResult.totalClauses) 个条款详情")
                        
                        if includeFullText {
                            reportContentRow("完整条款原文")
                        }
                        
                        if includeAIAnalysis {
                            reportContentRow("AI深度分析")
                        }
                    }
                }
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
    }
    
    // MARK: - 导出按钮
    private var exportButton: some View {
        Button(action: startExport) {
            Text("导出报告")
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
        }
    }
    
    // MARK: - 导出进度视图
    private var exportProgressView: some View {
        VStack(spacing: 16) {
            ProgressView(value: exportProgress)
                .progressViewStyle(LinearProgressViewStyle())
            
            Text("正在生成报告...")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text("\(Int(exportProgress * 100))%")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.blue)
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 辅助方法
    private func formatCard(for format: ExportFormat) -> some View {
        Button(action: {
            selectedFormat = format
        }) {
            HStack(spacing: 16) {
                Image(systemName: format.icon)
                    .font(.title2)
                    .foregroundColor(.blue)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(format.rawValue)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(format.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: selectedFormat == format ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(selectedFormat == format ? .blue : .gray)
                    .font(.title2)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(selectedFormat == format ? Color.blue : Color.gray.opacity(0.3), lineWidth: selectedFormat == format ? 2 : 1)
                    .background(selectedFormat == format ? Color.blue.opacity(0.05) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func optionToggle(title: String, description: String, isOn: Binding<Bool>) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: isOn)
                .labelsHidden()
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }
    
    private func reportContentRow(_ text: String) -> some View {
        HStack(spacing: 8) {
            Image(systemName: "checkmark.circle.fill")
                .font(.caption)
                .foregroundColor(.green)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var estimatedFileSize: String {
        let baseSize = 500 // KB
        var totalSize = baseSize
        
        if includeFullText {
            totalSize += 200
        }
        
        if includeAIAnalysis {
            totalSize += 300
        }
        
        if includeRiskSummary {
            totalSize += 100
        }
        
        return "\(totalSize) KB"
    }
    
    private func startExport() {
        isExporting = true
        exportProgress = 0.0
        
        // 模拟导出进度
        Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { timer in
            exportProgress += 0.05
            
            if exportProgress >= 1.0 {
                timer.invalidate()
                
                // 导出完成
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    isExporting = false
                    dismiss()
                    
                    // 这里应该触发实际的文件导出和分享
                    // 例如：presentShareSheet()
                }
            }
        }
    }
}

#Preview {
    ExportReportView(analysisResult: TermsAnalysisResult(
        fileName: "微信用户协议.pdf",
        fileSize: 2048000,
        uploadDate: Date(),
        totalClauses: 15,
        highRiskCount: 3,
        mediumRiskCount: 7,
        lowRiskCount: 5,
        clauses: MockDataService.shared.featuredClauses,
        overallRiskScore: 0.65,
        aiSummary: "该协议包含多项需要注意的条款，特别是在数据收集和免责声明方面存在一定风险。"
    ))
    .environmentObject(UserState())
}
