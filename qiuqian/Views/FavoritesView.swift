//
//  FavoritesView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct FavoritesView: View {
    @EnvironmentObject var userState: UserState
    @EnvironmentObject var dataService: MockDataService
    
    var favoriteClauses: [TermsClause] {
        dataService.featuredClauses.filter { clause in
            userState.isClauseFavorited(clause.id)
        }
    }
    
    var body: some View {
        NavigationView {
            Group {
                if favoriteClauses.isEmpty {
                    emptyStateView
                } else {
                    clausesList
                }
            }
            .navigationTitle("收藏")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        EmptyStateView.noFavorites {
            // 这里可以添加跳转到首页的逻辑
        }
    }
    
    // MARK: - 条款列表
    private var clausesList: some View {
        List {
            ForEach(favoriteClauses) { clause in
                NavigationLink(destination: ClauseDetailView(clause: clause)) {
                    ClauseRowView(clause: clause)
                        .environmentObject(userState)
                }
            }
            .onDelete(perform: removeFavorites)
        }
        .listStyle(PlainListStyle())
    }
    
    // MARK: - 移除收藏
    private func removeFavorites(at offsets: IndexSet) {
        for index in offsets {
            let clause = favoriteClauses[index]
            userState.removeFavoriteClause(clause.id)
        }
    }
}

#Preview {
    FavoritesView()
        .environmentObject(UserState())
        .environmentObject(MockDataService.shared)
}
