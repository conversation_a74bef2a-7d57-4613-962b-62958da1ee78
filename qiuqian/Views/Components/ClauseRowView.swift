//
//  ClauseRowView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct ClauseRowView: View {
    let clause: TermsClause
    @EnvironmentObject var userState: UserState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和风险等级
            HStack(alignment: .top, spacing: 12) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(clause.title)
                        .font(.headline)
                        .fontWeight(.medium)
                        .lineLimit(2)
                    
                    Text(clause.appName)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 风险等级标识
                HStack(spacing: 4) {
                    Image(systemName: clause.riskLevel.icon)
                        .font(.caption)
                    Text(clause.riskLevel.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(clause.riskLevel.color.opacity(0.2))
                .foregroundColor(clause.riskLevel.color)
                .cornerRadius(6)
            }
            
            // AI摘要
            Text(clause.aiSummary)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
            
            // 底部信息栏
            HStack {
                // 分类标签
                Text(clause.category.displayName)
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(4)
                
                Spacer()
                
                // 收藏按钮
                Button(action: {
                    if userState.isClauseFavorited(clause.id) {
                        userState.removeFavoriteClause(clause.id)
                    } else {
                        userState.addFavoriteClause(clause.id)
                    }
                }) {
                    Image(systemName: userState.isClauseFavorited(clause.id) ? "heart.fill" : "heart")
                        .foregroundColor(userState.isClauseFavorited(clause.id) ? .red : .gray)
                }
                .buttonStyle(PlainButtonStyle())
                
                // 分享按钮
                Button(action: {
                    // 分享功能
                }) {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(.gray)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    ClauseRowView(clause: TermsClause(
        title: "用户数据收集条款",
        content: "我们可能会收集您的个人信息，包括但不限于姓名、邮箱地址、设备信息等，用于改善服务质量。",
        summary: "应用会收集用户个人信息用于服务改善",
        riskLevel: .medium,
        category: .privacy,
        appName: "微信",
        appCategory: "社交",
        aiAnalysis: "该条款允许应用收集广泛的个人信息，建议用户仔细考虑隐私风险。",
        aiSummary: "中等隐私风险，需注意个人信息保护",
        keywords: ["个人信息", "数据收集", "隐私"],
        createdAt: Date(),
        updatedAt: Date()
    ))
    .environmentObject(UserState())
    .padding()
}
