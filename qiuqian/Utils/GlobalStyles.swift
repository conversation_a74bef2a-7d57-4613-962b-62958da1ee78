//
//  GlobalStyles.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

// MARK: - 全局组件样式

/// 应用图标视图
struct AppIconView: View {
    let appName: String
    let size: CGFloat
    
    init(appName: String, size: CGFloat = Theme.Layout.appIconSize) {
        self.appName = appName
        self.size = size
    }
    
    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: Theme.Layout.appIconCornerRadius)
                .fill(appIconBackgroundColor)
                .frame(width: size, height: size)
            
            // 图标或占位符
            if let appIcon = getAppIcon(for: appName) {
                Image(systemName: appIcon)
                    .font(.system(size: size * 0.4, weight: .medium))
                    .foregroundColor(.white)
            } else {
                // 使用应用名称首字母作为占位符
                Text(String(appName.prefix(1)))
                    .font(.system(size: size * 0.4, weight: .bold))
                    .foregroundColor(.white)
            }
        }
    }
    
    // MARK: - 辅助方法
    private var appIconBackgroundColor: Color {
        // 根据应用名称生成颜色
        let colors: [Color] = [
            Theme.primaryBlue, Theme.success, Theme.warning, 
            Theme.error, .purple, .pink, .indigo, .teal
        ]
        let index = abs(appName.hashValue) % colors.count
        return colors[index]
    }
    
    private func getAppIcon(for appName: String) -> String? {
        // 根据应用名称返回对应的系统图标
        switch appName.lowercased() {
        case "微信", "wechat":
            return "message.fill"
        case "支付宝", "alipay":
            return "creditcard.fill"
        case "淘宝", "taobao":
            return "bag.fill"
        case "抖音", "tiktok":
            return "music.note"
        case "netflix":
            return "tv.fill"
        case "qq":
            return "bubble.left.and.bubble.right.fill"
        case "百度", "baidu":
            return "magnifyingglass"
        case "美团", "meituan":
            return "fork.knife"
        case "滴滴", "didi":
            return "car.fill"
        case "京东", "jd":
            return "shippingbox.fill"
        default:
            return nil
        }
    }
}

/// 风险等级标签
struct RiskLevelBadge: View {
    let riskLevel: RiskLevel
    let style: BadgeStyle
    
    enum BadgeStyle {
        case compact    // 紧凑样式，只显示图标和文字
        case full      // 完整样式，带背景色
    }
    
    init(_ riskLevel: RiskLevel, style: BadgeStyle = .compact) {
        self.riskLevel = riskLevel
        self.style = style
    }
    
    var body: some View {
        HStack(spacing: Theme.Layout.paddingXS) {
            Image(systemName: riskLevel.icon)
                .font(Theme.Typography.caption)
                .foregroundColor(riskLevel.color)
            
            Text(riskLevel.displayName)
                .font(Theme.Typography.caption)
                .fontWeight(.medium)
                .foregroundColor(style == .full ? .white : riskLevel.color)
        }
        .padding(.horizontal, style == .full ? Theme.Layout.paddingS : 0)
        .padding(.vertical, style == .full ? Theme.Layout.paddingXS : 0)
        .background(
            style == .full ? 
            RoundedRectangle(cornerRadius: Theme.Layout.cornerRadiusS)
                .fill(riskLevel.color) : nil
        )
    }
}

/// 分类标签
struct CategoryTag: View {
    let category: ClauseCategory
    let isSelected: Bool
    
    var body: some View {
        Text(category.displayName)
            .font(Theme.Typography.caption)
            .fontWeight(.medium)
            .padding(.horizontal, Theme.Layout.paddingS)
            .padding(.vertical, Theme.Layout.paddingXS)
            .background(
                RoundedRectangle(cornerRadius: Theme.Layout.cornerRadiusS)
                    .fill(isSelected ? Theme.primaryBlue.opacity(0.2) : Theme.backgroundSecondary)
            )
            .foregroundColor(isSelected ? Theme.primaryBlue : Theme.textSecondary)
    }
}

/// 统计卡片
struct StatCard: View {
    let icon: String
    let title: String
    let value: String
    let subtitle: String?
    let color: Color
    
    init(icon: String, title: String, value: String, subtitle: String? = nil, color: Color = Theme.primaryBlue) {
        self.icon = icon
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.color = color
    }
    
    var body: some View {
        VStack(spacing: Theme.Layout.paddingS) {
            Image(systemName: icon)
                .font(Theme.Typography.title3)
                .foregroundColor(color)
            
            Text(title)
                .font(Theme.Typography.caption)
                .foregroundColor(Theme.textSecondary)
                .multilineTextAlignment(.center)
            
            Text(value)
                .font(Theme.Typography.headline)
                .fontWeight(.bold)
                .foregroundColor(Theme.textPrimary)
            
            if let subtitle = subtitle, !subtitle.isEmpty {
                Text(subtitle)
                    .font(Theme.Typography.caption2)
                    .foregroundColor(Theme.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(Theme.Layout.paddingL)
        .background(
            RoundedRectangle(cornerRadius: Theme.Layout.cornerRadiusL)
                .fill(color.opacity(0.1))
        )
    }
}

/// 菜单行
struct MenuRow: View {
    let icon: String
    let title: String
    let subtitle: String?
    let iconColor: Color
    
    init(icon: String, title: String, subtitle: String? = nil, iconColor: Color = Theme.primaryBlue) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.iconColor = iconColor
    }
    
    var body: some View {
        HStack(spacing: Theme.Layout.paddingM) {
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .frame(width: Theme.Layout.iconM)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(Theme.Typography.body)
                    .foregroundColor(Theme.textPrimary)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(Theme.Typography.caption)
                        .foregroundColor(Theme.textSecondary)
                }
            }
            
            Spacer()
        }
    }
}

/// 分隔线
struct CustomDivider: View {
    let indent: CGFloat
    
    init(indent: CGFloat = 0) {
        self.indent = indent
    }
    
    var body: some View {
        Rectangle()
            .fill(Theme.textTertiary.opacity(0.3))
            .frame(height: Theme.Layout.dividerHeight)
            .padding(.leading, indent)
    }
}

/// 加载状态视图
struct LoadingStateView: View {
    let message: String
    
    init(message: String = "加载中...") {
        self.message = message
    }
    
    var body: some View {
        VStack(spacing: Theme.Layout.paddingL) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text(message)
                .font(Theme.Typography.subheadline)
                .foregroundColor(Theme.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Theme.backgroundPrimary)
    }
}

// MARK: - 视图扩展
extension View {
    /// 应用卡片样式
    func cardStyle() -> some View {
        self
            .background(Theme.backgroundPrimary)
            .cornerRadius(Theme.Layout.cornerRadiusL)
            .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    /// 应用列表项样式
    func listItemStyle() -> some View {
        self
            .padding(.horizontal, Theme.Layout.paddingXL)
            .padding(.vertical, Theme.Layout.paddingL)
            .background(Theme.backgroundPrimary)
    }
}
