//
//  EmptyStateView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct EmptyStateView: View {
    let icon: String
    let title: String
    let description: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    @State private var isVisible = false
    
    init(
        icon: String,
        title: String,
        description: String,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.icon = icon
        self.title = title
        self.description = description
        self.actionTitle = actionTitle
        self.action = action
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 图标
            Image(systemName: icon)
                .font(.system(size: 60, weight: .light))
                .foregroundColor(Theme.textTertiary)
                .scaleEffect(isVisible ? 1.0 : 0.8)
                .animation(Animations.bouncy.delay(0.1), value: isVisible)
            
            // 文本内容
            VStack(spacing: 12) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(Theme.textPrimary)
                    .fadeIn(isVisible: isVisible, delay: 0.2)
                
                Text(description)
                    .font(.body)
                    .foregroundColor(Theme.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
                    .fadeIn(isVisible: isVisible, delay: 0.3)
            }
            
            // 行动按钮
            if let actionTitle = actionTitle, let action = action {
                Button(action: action) {
                    Text(actionTitle)
                }
                .primaryButtonStyle()
                .fadeIn(isVisible: isVisible, delay: 0.4)
            }
        }
        .padding(.horizontal, 40)
        .onAppear {
            withAnimation {
                isVisible = true
            }
        }
    }
}

// MARK: - 预定义空状态
extension EmptyStateView {
    static func noFavorites(action: @escaping () -> Void) -> EmptyStateView {
        EmptyStateView(
            icon: "heart",
            title: "暂无收藏条款",
            description: "在浏览条款时点击收藏按钮\n即可将条款添加到这里",
            actionTitle: "去浏览条款",
            action: action
        )
    }
    
    static func noUploadHistory(action: @escaping () -> Void) -> EmptyStateView {
        EmptyStateView(
            icon: "doc.badge.plus",
            title: "暂无上传记录",
            description: "上传PDF、Word文档或图片\n开始AI智能条款分析",
            actionTitle: "立即上传",
            action: action
        )
    }
    
    static func noSearchResults() -> EmptyStateView {
        EmptyStateView(
            icon: "magnifyingglass",
            title: "未找到相关条款",
            description: "尝试使用其他关键词搜索\n或调整筛选条件"
        )
    }
    
    static func networkError(action: @escaping () -> Void) -> EmptyStateView {
        EmptyStateView(
            icon: "wifi.slash",
            title: "网络连接失败",
            description: "请检查网络连接后重试",
            actionTitle: "重新加载",
            action: action
        )
    }
    
    static func loadingError(action: @escaping () -> Void) -> EmptyStateView {
        EmptyStateView(
            icon: "exclamationmark.triangle",
            title: "加载失败",
            description: "数据加载出现问题，请重试",
            actionTitle: "重新加载",
            action: action
        )
    }
}

#Preview {
    VStack(spacing: 40) {
        EmptyStateView.noFavorites {
            print("Go to browse")
        }
        
        EmptyStateView.noSearchResults()
    }
    .padding()
}
