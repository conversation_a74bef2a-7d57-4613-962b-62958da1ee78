//
//  FeatureComparisonView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct FeatureComparisonView: View {
    @EnvironmentObject var userState: UserState
    @State private var showingUpgradeSheet = false
    
    struct Feature {
        let icon: String
        let title: String
        let description: String
        let freeAvailable: Bool
        let premiumAvailable: Bool
    }
    
    private let features: [Feature] = [
        Feature(
            icon: "eye.fill",
            title: "浏览精选条款",
            description: "查看来自热门应用的条款解析",
            freeAvailable: true,
            premiumAvailable: true
        ),
        Feature(
            icon: "magnifyingglass",
            title: "搜索筛选",
            description: "按关键词、风险等级、分类筛选",
            freeAvailable: true,
            premiumAvailable: true
        ),
        Feature(
            icon: "heart.fill",
            title: "收藏条款",
            description: "收藏重要条款，随时查看",
            freeAvailable: true,
            premiumAvailable: true
        ),
        Feature(
            icon: "doc.badge.plus",
            title: "文件上传分析",
            description: "上传PDF、Word等文件进行AI分析",
            freeAvailable: false,
            premiumAvailable: true
        ),
        Feature(
            icon: "brain.head.profile",
            title: "AI深度分析",
            description: "智能识别风险条款，生成详细报告",
            freeAvailable: false,
            premiumAvailable: true
        ),
        Feature(
            icon: "bubble.left.and.bubble.right.fill",
            title: "AI追问对话",
            description: "与AI深入讨论条款细节和风险",
            freeAvailable: false,
            premiumAvailable: true
        ),
        Feature(
            icon: "doc.text.fill",
            title: "导出分析报告",
            description: "生成专业PDF报告，便于保存分享",
            freeAvailable: false,
            premiumAvailable: true
        ),
        Feature(
            icon: "clock.fill",
            title: "无限历史记录",
            description: "保存所有分析历史，随时回顾",
            freeAvailable: false,
            premiumAvailable: true
        )
    ]
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题
            headerSection
            
            // 功能对比表
            comparisonTable
            
            // 升级按钮
            if userState.isFreeUser {
                upgradeSection
            }
        }
        .padding()
        .sheet(isPresented: $showingUpgradeSheet) {
            UpgradeView()
                .environmentObject(userState)
        }
    }
    
    // MARK: - 标题区域
    private var headerSection: some View {
        VStack(spacing: 8) {
            Text("功能对比")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("了解免费版和付费版的功能差异")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - 对比表格
    private var comparisonTable: some View {
        VStack(spacing: 0) {
            // 表头
            HStack {
                Text("功能")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Text("免费版")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .frame(width: 60)
                
                Text("付费版")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .frame(width: 60)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            
            // 功能行
            ForEach(Array(features.enumerated()), id: \.offset) { index, feature in
                featureRow(feature, isEven: index % 2 == 0)
            }
        }
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - 升级区域
    private var upgradeSection: some View {
        VStack(spacing: 12) {
            Text("升级到付费版，解锁所有功能")
                .font(.headline)
                .multilineTextAlignment(.center)
            
            Button("立即升级") {
                showingUpgradeSheet = true
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
            .font(.headline)
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 辅助方法
    private func featureRow(_ feature: Feature, isEven: Bool) -> some View {
        HStack(alignment: .top) {
            // 功能信息
            HStack(spacing: 12) {
                Image(systemName: feature.icon)
                    .font(.title3)
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(feature.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Text(feature.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 免费版状态
            availabilityIcon(feature.freeAvailable)
                .frame(width: 60)
            
            // 付费版状态
            availabilityIcon(feature.premiumAvailable)
                .frame(width: 60)
        }
        .padding()
        .background(isEven ? Color.clear : Color.gray.opacity(0.02))
    }
    
    private func availabilityIcon(_ available: Bool) -> some View {
        Image(systemName: available ? "checkmark.circle.fill" : "xmark.circle.fill")
            .font(.title3)
            .foregroundColor(available ? .green : .red)
    }
}

#Preview {
    FeatureComparisonView()
        .environmentObject(UserState())
}
