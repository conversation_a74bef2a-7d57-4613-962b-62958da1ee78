//
//  Theme.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct Theme {
    // MARK: - 主色调
    static let primaryBlue = Color(red: 0.0, green: 0.48, blue: 1.0)
    static let secondaryBlue = Color(red: 0.0, green: 0.48, blue: 1.0).opacity(0.8)

    // MARK: - 风险等级颜色
    static let highRisk = Color(red: 1.0, green: 0.23, blue: 0.19)
    static let mediumRisk = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let lowRisk = Color(red: 0.20, green: 0.78, blue: 0.35)

    // MARK: - 背景颜色
    static let backgroundPrimary = Color(.systemBackground)
    static let backgroundSecondary = Color(.secondarySystemBackground)
    static let backgroundTertiary = Color(.tertiarySystemBackground)

    // MARK: - 文本颜色
    static let textPrimary = Color(.label)
    static let textSecondary = Color(.secondaryLabel)
    static let textTertiary = Color(.tertiaryLabel)

    // MARK: - 功能颜色
    static let success = Color(red: 0.20, green: 0.78, blue: 0.35)
    static let warning = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let error = Color(red: 1.0, green: 0.23, blue: 0.19)
    static let info = Color(red: 0.0, green: 0.48, blue: 1.0)

    // MARK: - 会员相关颜色
    static let premium = Color(red: 1.0, green: 0.84, blue: 0.0)
    static let trial = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let free = Color(.systemGray)

    // MARK: - 渐变色
    static let primaryGradient = LinearGradient(
        colors: [primaryBlue, secondaryBlue],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    static let riskGradient = LinearGradient(
        colors: [highRisk.opacity(0.3), mediumRisk.opacity(0.3), lowRisk.opacity(0.3)],
        startPoint: .leading,
        endPoint: .trailing
    )

    // MARK: - 布局常量
    struct Layout {
        // 间距
        static let paddingXS: CGFloat = 4
        static let paddingS: CGFloat = 8
        static let paddingM: CGFloat = 12
        static let paddingL: CGFloat = 16
        static let paddingXL: CGFloat = 20
        static let paddingXXL: CGFloat = 24

        // 圆角
        static let cornerRadiusS: CGFloat = 6
        static let cornerRadiusM: CGFloat = 8
        static let cornerRadiusL: CGFloat = 12
        static let cornerRadiusXL: CGFloat = 16

        // 图标尺寸
        static let iconS: CGFloat = 16
        static let iconM: CGFloat = 20
        static let iconL: CGFloat = 24
        static let iconXL: CGFloat = 32

        // 应用图标尺寸
        static let appIconSize: CGFloat = 60
        static let appIconCornerRadius: CGFloat = 12

        // 分隔线
        static let dividerHeight: CGFloat = 0.5
        static let dividerIndent: CGFloat = 20
    }

    // MARK: - 字体样式
    struct Typography {
        static let largeTitle = Font.largeTitle.weight(.bold)
        static let title1 = Font.title.weight(.semibold)
        static let title2 = Font.title2.weight(.semibold)
        static let title3 = Font.title3.weight(.medium)
        static let headline = Font.headline.weight(.medium)
        static let body = Font.body
        static let bodyMedium = Font.body.weight(.medium)
        static let callout = Font.callout
        static let subheadline = Font.subheadline
        static let footnote = Font.footnote
        static let caption = Font.caption
        static let caption2 = Font.caption2
    }
}

// MARK: - 视图修饰符
struct CardStyle: ViewModifier {
    let backgroundColor: Color
    let cornerRadius: CGFloat
    let shadowRadius: CGFloat
    
    init(
        backgroundColor: Color = Theme.backgroundSecondary,
        cornerRadius: CGFloat = 12,
        shadowRadius: CGFloat = 2
    ) {
        self.backgroundColor = backgroundColor
        self.cornerRadius = cornerRadius
        self.shadowRadius = shadowRadius
    }
    
    func body(content: Content) -> some View {
        content
            .background(backgroundColor)
            .cornerRadius(cornerRadius)
            .shadow(color: .black.opacity(0.05), radius: shadowRadius, x: 0, y: 1)
    }
}

struct PrimaryButtonStyle: ButtonStyle {
    let backgroundColor: Color
    let foregroundColor: Color
    let isDisabled: Bool
    
    init(
        backgroundColor: Color = Theme.primaryBlue,
        foregroundColor: Color = .white,
        isDisabled: Bool = false
    ) {
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.isDisabled = isDisabled
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .foregroundColor(isDisabled ? .gray : foregroundColor)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isDisabled ? Color.gray.opacity(0.3) : backgroundColor)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    let borderColor: Color
    let foregroundColor: Color
    
    init(
        borderColor: Color = Theme.primaryBlue,
        foregroundColor: Color = Theme.primaryBlue
    ) {
        self.borderColor = borderColor
        self.foregroundColor = foregroundColor
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .foregroundColor(foregroundColor)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: 1)
                    .background(Color.clear)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 视图扩展
extension View {
    func cardStyle(
        backgroundColor: Color = Theme.backgroundSecondary,
        cornerRadius: CGFloat = 12,
        shadowRadius: CGFloat = 2
    ) -> some View {
        modifier(CardStyle(
            backgroundColor: backgroundColor,
            cornerRadius: cornerRadius,
            shadowRadius: shadowRadius
        ))
    }
    
    func primaryButtonStyle(
        backgroundColor: Color = Theme.primaryBlue,
        foregroundColor: Color = .white,
        isDisabled: Bool = false
    ) -> some View {
        buttonStyle(PrimaryButtonStyle(
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor,
            isDisabled: isDisabled
        ))
    }
    
    func secondaryButtonStyle(
        borderColor: Color = Theme.primaryBlue,
        foregroundColor: Color = Theme.primaryBlue
    ) -> some View {
        buttonStyle(SecondaryButtonStyle(
            borderColor: borderColor,
            foregroundColor: foregroundColor
        ))
    }
}
