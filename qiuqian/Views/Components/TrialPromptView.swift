//
//  TrialPromptView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct TrialPromptView: View {
    @EnvironmentObject var userState: UserState
    @State private var showingUpgradeSheet = false
    let onStartTrial: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // 试用介绍
            VStack(spacing: 12) {
                Image(systemName: "gift.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.orange)
                
                Text("免费试用机会")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("体验完整的AI条款分析功能")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 试用内容
            VStack(alignment: .leading, spacing: 12) {
                Text("试用包含：")
                    .font(.headline)
                
                VStack(spacing: 8) {
                    trialFeatureRow(icon: "doc.badge.plus", text: "上传1个文件进行AI分析")
                    trialFeatureRow(icon: "brain.head.profile", text: "获得完整的风险评估报告")
                    trialFeatureRow(icon: "bubble.left.and.bubble.right", text: "与AI对话深入了解条款")
                    trialFeatureRow(icon: "doc.text", text: "导出专业分析报告")
                }
            }
            .padding()
            .background(Color.orange.opacity(0.1))
            .cornerRadius(12)
            
            // 行动按钮
            VStack(spacing: 12) {
                Button("开始免费试用") {
                    onStartTrial()
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.orange)
                .foregroundColor(.white)
                .cornerRadius(12)
                .font(.headline)
                
                Button("直接升级付费版") {
                    showingUpgradeSheet = true
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.clear)
                .foregroundColor(.blue)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue, lineWidth: 1)
                )
            }
            
            // 说明文字
            Text("试用后可随时升级到付费版，享受无限制使用")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .sheet(isPresented: $showingUpgradeSheet) {
            UpgradeView()
                .environmentObject(userState)
        }
    }
    
    private func trialFeatureRow(icon: String, text: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.orange)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
            
            Spacer()
        }
    }
}

#Preview {
    TrialPromptView(onStartTrial: {})
        .environmentObject(UserState())
}
