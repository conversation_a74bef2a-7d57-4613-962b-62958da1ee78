//
//  MoreView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct MoreView: View {
    @EnvironmentObject var userState: UserState
    @EnvironmentObject var dataService: MockDataService
    @State private var showingUpgradeSheet = false
    
    var body: some View {
        NavigationView {
            List {
                // 用户状态卡片
                userStatusSection
                
                // 功能菜单
                functionsSection
                
                // 收藏和历史
                dataSection
                
                // 设置菜单
                settingsSection
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("更多")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingUpgradeSheet) {
            UpgradeView()
                .environmentObject(userState)
        }
    }
    
    // MARK: - 用户状态区域
    private var userStatusSection: some View {
        Section {
            HStack(spacing: 16) {
                // 用户头像
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("用户")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    HStack {
                        Text(userState.subscriptionStatus.displayName)
                            .font(.subheadline)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(statusColor.opacity(0.2))
                            .foregroundColor(statusColor)
                            .cornerRadius(6)
                        
                        if userState.isTrialUser {
                            Text("剩余 \(userState.remainingTrialUsage) 次")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
                
                if userState.isFreeUser {
                    Button("升级") {
                        showingUpgradeSheet = true
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - 功能菜单
    private var functionsSection: some View {
        Section("功能") {
            if userState.isPremiumUser {
                NavigationLink(destination: Text("导出报告")) {
                    MenuRow(icon: "doc.text.fill", title: "导出报告", subtitle: "生成PDF报告")
                }
            }

            NavigationLink(destination: FeatureComparisonView().environmentObject(userState)) {
                MenuRow(icon: "list.bullet.rectangle", title: "功能对比", subtitle: "查看版本差异")
            }

            if !userState.isPremiumUser {
                Button(action: {
                    showingUpgradeSheet = true
                }) {
                    MenuRow(icon: "crown.fill", title: "升级会员", subtitle: "解锁全部功能", iconColor: Theme.premium)
                        .foregroundColor(Theme.textPrimary)
                }
            }
        }
    }
    
    // MARK: - 数据区域
    private var dataSection: some View {
        Section("数据") {
            NavigationLink(destination: FavoritesView().environmentObject(userState).environmentObject(dataService)) {
                MenuRow(icon: "heart.fill", title: "收藏条款", subtitle: "\(userState.favoriteClauseIds.count) 个条款", iconColor: Theme.error)
            }

            NavigationLink(destination: Text("上传历史")) {
                MenuRow(icon: "clock.fill", title: "上传历史", subtitle: "\(userState.uploadHistory.count) 个文件")
            }
        }
    }
    
    // MARK: - 设置菜单
    private var settingsSection: some View {
        Section("设置") {
            NavigationLink(destination: Text("通知设置")) {
                MenuRow(icon: "bell.fill", title: "通知设置")
            }

            NavigationLink(destination: Text("隐私设置")) {
                MenuRow(icon: "lock.fill", title: "隐私设置")
            }

            NavigationLink(destination: Text("帮助中心")) {
                MenuRow(icon: "questionmark.circle.fill", title: "帮助中心")
            }

            NavigationLink(destination: Text("关于我们")) {
                MenuRow(icon: "info.circle.fill", title: "关于我们")
            }
        }
    }
    
    // MARK: - 辅助方法
    private var statusColor: Color {
        switch userState.subscriptionStatus {
        case .free:
            return .gray
        case .premium:
            return .blue
        case .trial:
            return .orange
        }
    }
    

}

#Preview {
    MoreView()
        .environmentObject(UserState())
        .environmentObject(MockDataService.shared)
}
