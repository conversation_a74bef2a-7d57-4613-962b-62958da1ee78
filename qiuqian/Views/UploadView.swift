//
//  UploadView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct UploadView: View {
    @EnvironmentObject var userState: UserState
    @State private var showingUpgradeSheet = false
    @State private var showingFilePicker = false
    @State private var isAnalyzing = false
    @State private var analysisProgress: Double = 0.0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if userState.canUploadFiles {
                    // 付费用户或试用用户的上传界面
                    premiumUploadInterface
                } else {
                    // 免费用户的升级提示界面
                    freeUserInterface
                }
            }
            .padding()
            .navigationTitle("文件分析")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingUpgradeSheet) {
            UpgradeView()
                .environmentObject(userState)
        }
    }
    
    // MARK: - 付费用户上传界面
    private var premiumUploadInterface: some View {
        VStack(spacing: 24) {
            // 用户状态提示
            if userState.isTrialUser {
                VStack(spacing: 8) {
                    Text("试用中")
                        .font(.headline)
                        .foregroundColor(.orange)
                    
                    Text("剩余 \(userState.remainingTrialUsage) 次试用机会")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(12)
            }
            
            // 上传区域
            if !isAnalyzing {
                uploadArea
            } else {
                analysisProgressView
            }
            
            // 支持的文件格式说明
            supportedFormatsInfo
            
            // 上传历史
            if !userState.uploadHistory.isEmpty {
                uploadHistorySection
            }
            
            Spacer()
        }
    }
    
    // MARK: - 免费用户界面
    private var freeUserInterface: some View {
        VStack(spacing: 24) {
            // 功能介绍
            VStack(spacing: 16) {
                Image(systemName: "doc.text.magnifyingglass")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                
                Text("AI智能条款分析")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("上传PDF、Word文档或图片，AI将为您分析其中的条款风险")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 功能特点
            VStack(alignment: .leading, spacing: 12) {
                featureRow(icon: "checkmark.circle.fill", text: "智能识别高风险条款", color: .green)
                featureRow(icon: "exclamationmark.triangle.fill", text: "详细风险等级分析", color: .orange)
                featureRow(icon: "bubble.left.and.bubble.right.fill", text: "AI追问深入解析", color: .blue)
                featureRow(icon: "doc.text.fill", text: "生成专业分析报告", color: .purple)
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
            
            // 升级按钮
            VStack(spacing: 12) {
                Button("开始免费试用") {
                    userState.startTrial()
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
                .font(.headline)
                
                Button("升级到付费会员") {
                    showingUpgradeSheet = true
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.clear)
                .foregroundColor(.blue)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue, lineWidth: 1)
                )
            }
            
            Spacer()
        }
    }
    
    // MARK: - 上传区域
    private var uploadArea: some View {
        VStack(spacing: 16) {
            Button(action: {
                showingFilePicker = true
            }) {
                VStack(spacing: 16) {
                    Image(systemName: "icloud.and.arrow.up")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)
                    
                    Text("点击上传文件")
                        .font(.headline)
                        .foregroundColor(.blue)
                    
                    Text("或拖拽文件到此处")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 200)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.blue, style: StrokeStyle(lineWidth: 2, dash: [10]))
                        .background(Color.blue.opacity(0.05))
                )
            }
        }
    }
    
    // MARK: - 分析进度视图
    private var analysisProgressView: some View {
        VStack(spacing: 20) {
            ProgressView(value: analysisProgress)
                .progressViewStyle(LinearProgressViewStyle())
                .scaleEffect(1.2)
            
            Text("AI正在分析中...")
                .font(.headline)
                .foregroundColor(.blue)
            
            Text("预计需要 30-60 秒")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(16)
    }
    
    // MARK: - 支持格式说明
    private var supportedFormatsInfo: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("支持的文件格式：")
                .font(.headline)
            
            HStack(spacing: 16) {
                formatTag("PDF", color: .red)
                formatTag("DOC", color: .blue)
                formatTag("DOCX", color: .blue)
                formatTag("JPG", color: .green)
                formatTag("PNG", color: .green)
            }
            
            Text("文件大小限制：最大 10MB")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 上传历史
    private var uploadHistorySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近分析")
                .font(.headline)
            
            ForEach(Array(userState.uploadHistory.prefix(3))) { result in
                HStack {
                    Image(systemName: "doc.text.fill")
                        .foregroundColor(.blue)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(result.fileName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text(result.uploadDate, style: .date)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Text("\(result.totalClauses) 条款")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.gray.opacity(0.05))
                .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 辅助方法
    private func featureRow(icon: String, text: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
            
            Spacer()
        }
    }
    
    private func formatTag(_ text: String, color: Color) -> some View {
        Text(text)
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(color.opacity(0.2))
            .foregroundColor(color)
            .cornerRadius(6)
    }
}

#Preview {
    UploadView()
        .environmentObject(UserState())
}
