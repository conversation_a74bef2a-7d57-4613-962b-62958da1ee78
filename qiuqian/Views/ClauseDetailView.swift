//
//  ClauseDetailView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct ClauseDetailView: View {
    let clause: TermsClause
    @EnvironmentObject var userState: UserState
    @State private var showingAIChat = false
    @State private var showingUpgradeSheet = false
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 条款基本信息
                clauseHeaderSection
                
                // 风险分析
                riskAnalysisSection
                
                // 条款内容
                clauseContentSection
                
                // AI分析
                aiAnalysisSection
                
                // AI追问功能（付费功能）
                aiChatSection
                
                // 相关条款推荐
                relatedClausesSection
            }
            .padding()
        }
        .navigationTitle("条款详情")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                HStack {
                    // 收藏按钮
                    Button(action: toggleFavorite) {
                        Image(systemName: userState.isClauseFavorited(clause.id) ? "heart.fill" : "heart")
                            .foregroundColor(userState.isClauseFavorited(clause.id) ? .red : .primary)
                    }
                    
                    // 分享按钮
                    Button(action: shareClause) {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .sheet(isPresented: $showingAIChat) {
            AIChatView(clause: clause)
                .environmentObject(userState)
        }
        .sheet(isPresented: $showingUpgradeSheet) {
            UpgradeView()
                .environmentObject(userState)
        }
    }
    
    // MARK: - 条款头部信息
    private var clauseHeaderSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 应用信息
            HStack {
                Text(clause.appName)
                    .font(Theme.Typography.subheadline)
                    .fontWeight(.medium)
                    .padding(.horizontal, Theme.Layout.paddingS)
                    .padding(.vertical, Theme.Layout.paddingXS)
                    .background(Theme.primaryBlue.opacity(0.1))
                    .foregroundColor(Theme.primaryBlue)
                    .cornerRadius(Theme.Layout.cornerRadiusS)

                Text(clause.appCategory)
                    .font(Theme.Typography.subheadline)
                    .foregroundColor(Theme.textSecondary)

                Spacer()
            }
            
            // 条款标题
            Text(clause.title)
                .font(Theme.Typography.title2)
                .foregroundColor(Theme.textPrimary)

            // 条款分类
            CategoryTag(category: clause.category, isSelected: false)
        }
    }
    
    // MARK: - 风险分析区域
    private var riskAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("风险评估")
                .font(.headline)
            
            HStack(spacing: 16) {
                // 风险等级
                HStack(spacing: 8) {
                    Image(systemName: clause.riskLevel.icon)
                        .font(.title2)
                        .foregroundColor(clause.riskLevel.color)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("风险等级")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(clause.riskLevel.displayName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(clause.riskLevel.color)
                    }
                }
                
                Spacer()
                
                // AI摘要
                VStack(alignment: .trailing, spacing: 2) {
                    Text("AI提示")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(clause.aiSummary)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .multilineTextAlignment(.trailing)
                }
            }
            .padding()
            .background(clause.riskLevel.color.opacity(0.1))
            .cornerRadius(12)
        }
    }
    
    // MARK: - 条款内容
    private var clauseContentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("条款原文")
                .font(.headline)
            
            Text(clause.content)
                .font(.body)
                .padding()
                .background(Color.gray.opacity(0.05))
                .cornerRadius(12)
        }
    }
    
    // MARK: - AI分析
    private var aiAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("AI详细分析")
                .font(.headline)
            
            Text(clause.aiAnalysis)
                .font(.body)
                .padding()
                .background(Color.blue.opacity(0.05))
                .cornerRadius(12)
            
            // 关键词标签
            if !clause.keywords.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("关键词")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                        ForEach(clause.keywords, id: \.self) { keyword in
                            Text(keyword)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.gray.opacity(0.2))
                                .cornerRadius(6)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - AI追问功能
    private var aiChatSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("AI深入解析")
                .font(.headline)
            
            if userState.canUseAIChat {
                Button(action: {
                    showingAIChat = true
                }) {
                    HStack {
                        Image(systemName: "bubble.left.and.bubble.right.fill")
                        Text("与AI对话，深入了解条款风险")
                        Spacer()
                        Image(systemName: "chevron.right")
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(12)
                }
            } else {
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "bubble.left.and.bubble.right.fill")
                            .foregroundColor(.gray)
                        Text("AI追问功能仅限付费用户")
                            .foregroundColor(.gray)
                        Spacer()
                    }
                    
                    Button("升级会员解锁") {
                        showingUpgradeSheet = true
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
    
    // MARK: - 相关条款推荐
    private var relatedClausesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("相关条款")
                .font(.headline)
            
            Text("暂无相关条款推荐")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.gray.opacity(0.05))
                .cornerRadius(12)
        }
    }
    
    // MARK: - 辅助方法
    private func toggleFavorite() {
        if userState.isClauseFavorited(clause.id) {
            userState.removeFavoriteClause(clause.id)
        } else {
            userState.addFavoriteClause(clause.id)
        }
    }
    
    private func shareClause() {
        // 实现分享功能
    }
}

#Preview {
    NavigationView {
        ClauseDetailView(clause: TermsClause(
            title: "用户数据收集条款",
            content: "我们可能会收集您的个人信息，包括但不限于姓名、邮箱地址、设备信息等，用于改善服务质量。我们承诺严格保护您的隐私信息，不会将其用于商业目的或与第三方分享。",
            summary: "应用会收集用户个人信息用于服务改善",
            riskLevel: .medium,
            category: .privacy,
            appName: "微信",
            appCategory: "社交",
            aiAnalysis: "该条款允许应用收集广泛的个人信息，虽然声明会保护隐私，但收集范围较广，建议用户仔细考虑隐私风险。特别需要注意的是，条款中使用了'包括但不限于'的表述，这意味着实际收集的信息可能超出明确列出的范围。",
            aiSummary: "中等隐私风险，需注意个人信息保护",
            keywords: ["个人信息", "数据收集", "隐私", "第三方"],
            createdAt: Date(),
            updatedAt: Date()
        ))
        .environmentObject(UserState())
    }
}
