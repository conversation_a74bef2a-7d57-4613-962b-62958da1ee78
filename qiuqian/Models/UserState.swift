//
//  UserState.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import Foundation
import Combine

/// 用户订阅状态
enum SubscriptionStatus: String, CaseIterable {
    case free = "free"
    case premium = "premium"
    case trial = "trial"
    
    var displayName: String {
        switch self {
        case .free:
            return "免费用户"
        case .premium:
            return "付费会员"
        case .trial:
            return "试用中"
        }
    }
}

/// 用户状态管理
class UserState: ObservableObject {
    @Published var subscriptionStatus: SubscriptionStatus = .free
    @Published var trialUsageCount: Int = 0
    @Published var maxTrialUsage: Int = 1
    @Published var favoriteClauseIds: Set<UUID> = []
    @Published var uploadHistory: [TermsAnalysisResult] = []
    
    /// 是否为付费用户
    var isPremiumUser: Bool {
        subscriptionStatus == .premium
    }
    
    /// 是否为试用用户
    var isTrialUser: Bool {
        subscriptionStatus == .trial
    }
    
    /// 是否为免费用户
    var isFreeUser: Bool {
        subscriptionStatus == .free
    }
    
    /// 是否可以上传文件
    var canUploadFiles: Bool {
        isPremiumUser || (isTrialUser && trialUsageCount < maxTrialUsage)
    }
    
    /// 是否可以使用AI追问功能
    var canUseAIChat: Bool {
        isPremiumUser
    }
    
    /// 是否可以导出报告
    var canExportReports: Bool {
        isPremiumUser
    }
    
    /// 剩余试用次数
    var remainingTrialUsage: Int {
        max(0, maxTrialUsage - trialUsageCount)
    }
    
    /// 升级到付费会员
    func upgradeToPremium() {
        subscriptionStatus = .premium
    }
    
    /// 开始试用
    func startTrial() {
        subscriptionStatus = .trial
        trialUsageCount = 0
    }
    
    /// 使用试用次数
    func useTrialUsage() {
        if isTrialUser {
            trialUsageCount += 1
        }
    }
    
    /// 添加收藏条款
    func addFavoriteClause(_ clauseId: UUID) {
        favoriteClauseIds.insert(clauseId)
    }
    
    /// 移除收藏条款
    func removeFavoriteClause(_ clauseId: UUID) {
        favoriteClauseIds.remove(clauseId)
    }
    
    /// 检查条款是否已收藏
    func isClauseFavorited(_ clauseId: UUID) -> Bool {
        favoriteClauseIds.contains(clauseId)
    }
    
    /// 添加上传历史
    func addUploadHistory(_ result: TermsAnalysisResult) {
        uploadHistory.insert(result, at: 0)
        // 限制历史记录数量
        if uploadHistory.count > 50 {
            uploadHistory = Array(uploadHistory.prefix(50))
        }
    }
}
