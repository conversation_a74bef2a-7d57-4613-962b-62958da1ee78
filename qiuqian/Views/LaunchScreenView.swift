//
//  LaunchScreenView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct LaunchScreenView: View {
    @State private var isAnimating = false
    @State private var showMainView = false
    
    var body: some View {
        if showMainView {
            MainTabView()
                .transition(.scaleAndFade)
        } else {
            ZStack {
                // 背景渐变
                Theme.primaryGradient
                    .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Logo动画
                    logoSection
                    
                    // 标题动画
                    titleSection
                    
                    // 加载指示器
                    LoadingDotsView()
                        .fadeIn(isVisible: isAnimating, delay: 1.0)
                }
            }
            .onAppear {
                startAnimations()
            }
        }
    }
    
    // MARK: - Logo区域
    private var logoSection: some View {
        ZStack {
            // 背景圆圈
            Circle()
                .fill(Color.white.opacity(0.2))
                .frame(width: 120, height: 120)
                .scaleEffect(isAnimating ? 1.0 : 0.8)
                .animation(Animations.spring.delay(0.2), value: isAnimating)
            
            // 主图标
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 50, weight: .light))
                .foregroundColor(.white)
                .scaleEffect(isAnimating ? 1.0 : 0.5)
                .animation(Animations.bouncy.delay(0.4), value: isAnimating)
        }
    }
    
    // MARK: - 标题区域
    private var titleSection: some View {
        VStack(spacing: 8) {
            Text("秋签")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .slideIn(isVisible: isAnimating, from: .top, distance: 30)
            
            Text("AI智能条款解析")
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.white.opacity(0.9))
                .slideIn(isVisible: isAnimating, from: .bottom, distance: 20)
        }
    }
    
    // MARK: - 动画控制
    private func startAnimations() {
        withAnimation {
            isAnimating = true
        }
        
        // 2秒后显示主界面
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(Animations.spring) {
                showMainView = true
            }
        }
    }
}

#Preview {
    LaunchScreenView()
}
