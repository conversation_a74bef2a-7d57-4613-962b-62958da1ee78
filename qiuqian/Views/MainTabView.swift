//
//  MainTabView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct MainTabView: View {
    @StateObject private var userState = UserState()
    @StateObject private var dataService = MockDataService.shared
    
    var body: some View {
        TabView {
            // 首页/数据浏览页
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("首页")
                }
                .environmentObject(userState)
                .environmentObject(dataService)

            // 统计页
            StatsView()
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("统计")
                }
                .environmentObject(userState)

            // 文件上传页（付费功能）
            UploadView()
                .tabItem {
                    Image(systemName: "plus.circle.fill")
                    Text("上传")
                }
                .environmentObject(userState)

            // 更多功能
            MoreView()
                .tabItem {
                    Image(systemName: "line.3.horizontal")
                    Text("更多")
                }
                .environmentObject(userState)
                .environmentObject(dataService)
        }
        .accentColor(.primary)
    }
}

#Preview {
    MainTabView()
}
