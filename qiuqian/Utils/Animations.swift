//
//  Animations.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct Animations {
    // MARK: - 基础动画
    static let spring = Animation.spring(response: 0.6, dampingFraction: 0.8)
    static let easeInOut = Animation.easeInOut(duration: 0.3)
    static let bouncy = Animation.interpolatingSpring(stiffness: 300, damping: 15)
    
    // MARK: - 特殊动画
    static let slideIn = Animation.easeOut(duration: 0.4)
    static let fadeIn = Animation.easeIn(duration: 0.2)
    static let scaleUp = Animation.spring(response: 0.4, dampingFraction: 0.6)
}

// MARK: - 动画视图修饰符
struct SlideInModifier: ViewModifier {
    let isVisible: Bool
    let direction: Edge
    let distance: CGFloat
    
    func body(content: Content) -> some View {
        content
            .offset(
                x: direction == .leading ? (isVisible ? 0 : -distance) : (direction == .trailing ? (isVisible ? 0 : distance) : 0),
                y: direction == .top ? (isVisible ? 0 : -distance) : (direction == .bottom ? (isVisible ? 0 : distance) : 0)
            )
            .opacity(isVisible ? 1 : 0)
            .animation(Animations.slideIn, value: isVisible)
    }
}

struct FadeInModifier: ViewModifier {
    let isVisible: Bool
    let delay: Double
    
    func body(content: Content) -> some View {
        content
            .opacity(isVisible ? 1 : 0)
            .animation(Animations.fadeIn.delay(delay), value: isVisible)
    }
}

struct ScaleEffectModifier: ViewModifier {
    let isPressed: Bool
    let scale: CGFloat
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? scale : 1.0)
            .animation(Animations.bouncy, value: isPressed)
    }
}

struct ShimmerModifier: ViewModifier {
    @State private var isAnimating = false
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.clear,
                                Color.white.opacity(0.3),
                                Color.clear
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(30))
                    .offset(x: isAnimating ? 200 : -200)
                    .animation(
                        Animation.linear(duration: 1.5).repeatForever(autoreverses: false),
                        value: isAnimating
                    )
            )
            .clipped()
            .onAppear {
                isAnimating = true
            }
    }
}

struct PulseModifier: ViewModifier {
    @State private var isPulsing = false
    let minOpacity: Double
    let maxOpacity: Double
    let duration: Double
    
    func body(content: Content) -> some View {
        content
            .opacity(isPulsing ? maxOpacity : minOpacity)
            .animation(
                Animation.easeInOut(duration: duration).repeatForever(autoreverses: true),
                value: isPulsing
            )
            .onAppear {
                isPulsing = true
            }
    }
}

// MARK: - 视图扩展
extension View {
    func slideIn(
        isVisible: Bool,
        from direction: Edge = .bottom,
        distance: CGFloat = 50
    ) -> some View {
        modifier(SlideInModifier(
            isVisible: isVisible,
            direction: direction,
            distance: distance
        ))
    }
    
    func fadeIn(isVisible: Bool, delay: Double = 0) -> some View {
        modifier(FadeInModifier(isVisible: isVisible, delay: delay))
    }
    
    func scaleOnPress(isPressed: Bool, scale: CGFloat = 0.95) -> some View {
        modifier(ScaleEffectModifier(isPressed: isPressed, scale: scale))
    }
    
    func shimmer() -> some View {
        modifier(ShimmerModifier())
    }
    
    func pulse(
        minOpacity: Double = 0.3,
        maxOpacity: Double = 1.0,
        duration: Double = 1.0
    ) -> some View {
        modifier(PulseModifier(
            minOpacity: minOpacity,
            maxOpacity: maxOpacity,
            duration: duration
        ))
    }
}

// MARK: - 自定义过渡效果
extension AnyTransition {
    static let slideAndFade = AnyTransition.asymmetric(
        insertion: .move(edge: .trailing).combined(with: .opacity),
        removal: .move(edge: .leading).combined(with: .opacity)
    )
    
    static let scaleAndFade = AnyTransition.scale.combined(with: .opacity)
    
    static let flipAndFade = AnyTransition.asymmetric(
        insertion: .modifier(
            active: FlipModifier(angle: -90, axis: (x: 0, y: 1, z: 0)),
            identity: FlipModifier(angle: 0, axis: (x: 0, y: 1, z: 0))
        ).combined(with: .opacity),
        removal: .modifier(
            active: FlipModifier(angle: 90, axis: (x: 0, y: 1, z: 0)),
            identity: FlipModifier(angle: 0, axis: (x: 0, y: 1, z: 0))
        ).combined(with: .opacity)
    )
}

struct FlipModifier: ViewModifier {
    let angle: Double
    let axis: (x: CGFloat, y: CGFloat, z: CGFloat)
    
    func body(content: Content) -> some View {
        content
            .rotation3DEffect(
                .degrees(angle),
                axis: axis,
                perspective: 1.0
            )
    }
}

// MARK: - 加载动画组件
struct LoadingDotsView: View {
    @State private var animating = false
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(Theme.primaryBlue)
                    .frame(width: 8, height: 8)
                    .scaleEffect(animating ? 1.0 : 0.5)
                    .animation(
                        Animation.easeInOut(duration: 0.6)
                            .repeatForever()
                            .delay(Double(index) * 0.2),
                        value: animating
                    )
            }
        }
        .onAppear {
            animating = true
        }
    }
}

struct ProgressRingView: View {
    let progress: Double
    let lineWidth: CGFloat
    let size: CGFloat
    
    init(progress: Double, lineWidth: CGFloat = 8, size: CGFloat = 60) {
        self.progress = progress
        self.lineWidth = lineWidth
        self.size = size
    }
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(Theme.backgroundTertiary, lineWidth: lineWidth)
            
            Circle()
                .trim(from: 0, to: progress)
                .stroke(
                    Theme.primaryGradient,
                    style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(Animations.spring, value: progress)
        }
        .frame(width: size, height: size)
    }
}
