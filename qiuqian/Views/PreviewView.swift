//
//  PreviewView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct PreviewView: View {
    var body: some View {
        MainTabView()
            .environmentObject(UserState())
            .environmentObject(MockDataService.shared)
    }
}

// MARK: - 单独预览组件
struct ComponentPreview: View {
    var body: some View {
        ScrollView {
            VStack(spacing: Theme.Layout.paddingXL) {
                // 应用图标展示
                VStack(alignment: .leading, spacing: Theme.Layout.paddingL) {
                    Text("应用图标")
                        .font(Theme.Typography.headline)

                    HStack(spacing: Theme.Layout.paddingL) {
                        AppIconView(appName: "微信")
                        AppIconView(appName: "支付宝")
                        AppIconView(appName: "淘宝")
                        AppIconView(appName: "抖音")
                        AppIconView(appName: "Unknown App")
                    }
                }

                // 风险等级标签
                VStack(alignment: .leading, spacing: Theme.Layout.paddingL) {
                    Text("风险等级标签")
                        .font(Theme.Typography.headline)

                    VStack(spacing: Theme.Layout.paddingS) {
                        HStack {
                            RiskLevelBadge(.high)
                            RiskLevelBadge(.medium)
                            RiskLevelBadge(.low)
                            Spacer()
                        }

                        HStack {
                            RiskLevelBadge(.high, style: .full)
                            RiskLevelBadge(.medium, style: .full)
                            RiskLevelBadge(.low, style: .full)
                            Spacer()
                        }
                    }
                }

                // 统计卡片
                VStack(alignment: .leading, spacing: Theme.Layout.paddingL) {
                    Text("统计卡片")
                        .font(Theme.Typography.headline)

                    HStack(spacing: Theme.Layout.paddingL) {
                        StatCard(
                            icon: "doc.badge.plus",
                            title: "已分析文件",
                            value: "12",
                            subtitle: "个文件",
                            color: Theme.primaryBlue
                        )

                        StatCard(
                            icon: "heart.fill",
                            title: "收藏条款",
                            value: "8",
                            subtitle: "个条款",
                            color: Theme.error
                        )
                    }
                }

                // 菜单行
                VStack(alignment: .leading, spacing: Theme.Layout.paddingL) {
                    Text("菜单行")
                        .font(Theme.Typography.headline)

                    VStack(spacing: Theme.Layout.paddingS) {
                        MenuRow(icon: "doc.text.fill", title: "导出报告", subtitle: "生成PDF报告")
                        MenuRow(icon: "heart.fill", title: "收藏条款", subtitle: "8 个条款", iconColor: Theme.error)
                        MenuRow(icon: "bell.fill", title: "通知设置")
                    }
                }
            }
            .padding(Theme.Layout.paddingXL)
        }
        .navigationTitle("组件预览")
    }
}

#Preview("主应用") {
    PreviewView()
}

#Preview("组件展示") {
    NavigationView {
        ComponentPreview()
    }
}
