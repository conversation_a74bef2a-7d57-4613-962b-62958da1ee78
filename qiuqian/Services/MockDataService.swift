//
//  MockDataService.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import Foundation

/// 模拟数据服务
class MockDataService: ObservableObject {
    static let shared = MockDataService()
    
    @Published var featuredClauses: [TermsClause] = []
    
    private init() {
        loadMockData()
    }
    
    /// 加载模拟数据
    private func loadMockData() {
        featuredClauses = [
            TermsClause(
                title: "用户数据收集条款",
                content: "我们可能会收集您的个人信息，包括但不限于姓名、邮箱地址、设备信息等，用于改善服务质量。",
                summary: "应用会收集用户个人信息用于服务改善",
                riskLevel: .medium,
                category: .privacy,
                appName: "微信",
                appCategory: "社交",
                aiAnalysis: "该条款允许应用收集广泛的个人信息，建议用户仔细考虑隐私风险。",
                aiSummary: "中等隐私风险，需注意个人信息保护",
                keywords: ["个人信息", "数据收集", "隐私"],
                createdAt: Date().addingTimeInterval(-86400 * 7),
                updatedAt: Date().addingTimeInterval(-86400 * 3)
            ),
            TermsClause(
                title: "服务免责声明",
                content: "本公司对因使用本服务而导致的任何直接或间接损失不承担责任，包括但不限于数据丢失、经济损失等。",
                summary: "公司对使用服务造成的损失不承担责任",
                riskLevel: .high,
                category: .liability,
                appName: "支付宝",
                appCategory: "金融",
                aiAnalysis: "该免责条款过于宽泛，几乎免除了公司的所有责任，对用户权益保护不利。",
                aiSummary: "高风险免责条款，用户权益保护不足",
                keywords: ["免责", "损失", "责任"],
                createdAt: Date().addingTimeInterval(-86400 * 5),
                updatedAt: Date().addingTimeInterval(-86400 * 1)
            ),
            TermsClause(
                title: "自动续费条款",
                content: "订阅服务将在到期前24小时自动续费，如需取消请提前在账户设置中关闭自动续费功能。",
                summary: "订阅服务会自动续费，需手动取消",
                riskLevel: .medium,
                category: .payment,
                appName: "Netflix",
                appCategory: "娱乐",
                aiAnalysis: "自动续费条款相对合理，但需要用户主动取消，建议设置提醒。",
                aiSummary: "中等风险，注意及时取消不需要的订阅",
                keywords: ["自动续费", "订阅", "取消"],
                createdAt: Date().addingTimeInterval(-86400 * 3),
                updatedAt: Date().addingTimeInterval(-86400 * 1)
            ),
            TermsClause(
                title: "账户安全条款",
                content: "用户有责任保护账户安全，包括设置强密码、定期更换密码等。因用户疏忽导致的账户安全问题，平台不承担责任。",
                summary: "用户需自行保护账户安全",
                riskLevel: .low,
                category: .usage,
                appName: "淘宝",
                appCategory: "购物",
                aiAnalysis: "该条款合理地要求用户承担账户安全责任，符合行业标准。",
                aiSummary: "低风险条款，用户需注意账户安全",
                keywords: ["账户安全", "密码", "责任"],
                createdAt: Date().addingTimeInterval(-86400 * 2),
                updatedAt: Date().addingTimeInterval(-86400 * 1)
            ),
            TermsClause(
                title: "服务终止条款",
                content: "公司有权在任何时候无需提前通知即可终止或暂停用户的服务访问权限。",
                summary: "公司可随时终止用户服务",
                riskLevel: .high,
                category: .termination,
                appName: "抖音",
                appCategory: "娱乐",
                aiAnalysis: "该条款给予公司过大的权力，可以随时终止服务而无需通知，对用户不公平。",
                aiSummary: "高风险条款，公司权力过大",
                keywords: ["服务终止", "暂停", "通知"],
                createdAt: Date().addingTimeInterval(-86400 * 1),
                updatedAt: Date()
            ),
            TermsClause(
                title: "位置信息收集",
                content: "应用将收集您的精确位置信息，用于提供个性化服务和广告推送。",
                summary: "应用收集用户位置信息用于服务和广告",
                riskLevel: .medium,
                category: .privacy,
                appName: "美团",
                appCategory: "生活服务",
                aiAnalysis: "位置信息属于敏感个人信息，需要明确告知用户具体用途和保护措施。",
                aiSummary: "中等隐私风险，位置信息需谨慎授权",
                keywords: ["位置信息", "个性化", "广告"],
                createdAt: Date().addingTimeInterval(-86400 * 4),
                updatedAt: Date().addingTimeInterval(-86400 * 2)
            ),
            TermsClause(
                title: "第三方数据共享",
                content: "我们可能与合作伙伴共享您的部分信息，以提供更好的服务体验。",
                summary: "可能与第三方共享用户信息",
                riskLevel: .high,
                category: .privacy,
                appName: "京东",
                appCategory: "购物",
                aiAnalysis: "第三方数据共享存在较高隐私风险，应明确说明共享范围和合作方信息。",
                aiSummary: "高隐私风险，数据共享范围不明确",
                keywords: ["第三方", "数据共享", "合作伙伴"],
                createdAt: Date().addingTimeInterval(-86400 * 6),
                updatedAt: Date().addingTimeInterval(-86400 * 4)
            )
        ]
    }
    
    /// 根据关键词搜索条款
    func searchClauses(keyword: String) -> [TermsClause] {
        if keyword.isEmpty {
            return featuredClauses
        }
        
        return featuredClauses.filter { clause in
            clause.title.localizedCaseInsensitiveContains(keyword) ||
            clause.content.localizedCaseInsensitiveContains(keyword) ||
            clause.summary.localizedCaseInsensitiveContains(keyword) ||
            clause.appName.localizedCaseInsensitiveContains(keyword) ||
            clause.keywords.contains { $0.localizedCaseInsensitiveContains(keyword) }
        }
    }
    
    /// 根据风险等级筛选条款
    func filterClauses(by riskLevel: RiskLevel?) -> [TermsClause] {
        guard let riskLevel = riskLevel else {
            return featuredClauses
        }
        
        return featuredClauses.filter { $0.riskLevel == riskLevel }
    }
    
    /// 根据分类筛选条款
    func filterClauses(by category: ClauseCategory?) -> [TermsClause] {
        guard let category = category else {
            return featuredClauses
        }
        
        return featuredClauses.filter { $0.category == category }
    }
    
    /// 根据应用名称筛选条款
    func filterClauses(by appName: String?) -> [TermsClause] {
        guard let appName = appName, !appName.isEmpty else {
            return featuredClauses
        }
        
        return featuredClauses.filter { $0.appName.localizedCaseInsensitiveContains(appName) }
    }
}
