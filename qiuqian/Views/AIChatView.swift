//
//  AIChatView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct AIChatView: View {
    let clause: TermsClause
    @EnvironmentObject var userState: UserState
    @Environment(\.dismiss) private var dismiss
    @State private var messages: [ChatMessage] = []
    @State private var inputText = ""
    @State private var isLoading = false
    
    struct ChatMessage: Identifiable {
        let id = UUID()
        let content: String
        let isUser: Bool
        let timestamp: Date
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 条款信息头部
                clauseHeaderView
                
                Divider()
                
                // 聊天消息列表
                chatMessagesView
                
                // 输入区域
                chatInputView
            }
            .navigationTitle("AI深入解析")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            initializeChat()
        }
    }
    
    // MARK: - 条款信息头部
    private var clauseHeaderView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(clause.appName)
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.2))
                    .foregroundColor(.blue)
                    .cornerRadius(4)
                
                Spacer()
                
                HStack(spacing: 4) {
                    Image(systemName: clause.riskLevel.icon)
                        .font(.caption)
                    Text(clause.riskLevel.displayName)
                        .font(.caption)
                }
                .foregroundColor(clause.riskLevel.color)
            }
            
            Text(clause.title)
                .font(.subheadline)
                .fontWeight(.medium)
                .lineLimit(2)
        }
        .padding()
        .background(Color.gray.opacity(0.05))
    }
    
    // MARK: - 聊天消息列表
    private var chatMessagesView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(messages) { message in
                        chatMessageRow(message)
                            .id(message.id)
                    }
                    
                    if isLoading {
                        loadingIndicator
                    }
                }
                .padding()
            }
            .onChange(of: messages.count) { _ in
                if let lastMessage = messages.last {
                    withAnimation {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }
    
    // MARK: - 聊天输入区域
    private var chatInputView: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 12) {
                TextField("询问AI关于这个条款的问题...", text: $inputText, axis: .vertical)
                    .textFieldStyle(PlainTextFieldStyle())
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(20)
                    .lineLimit(1...4)
                
                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? .gray : .blue)
                }
                .disabled(inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isLoading)
            }
            .padding()
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - 聊天消息行
    private func chatMessageRow(_ message: ChatMessage) -> some View {
        HStack {
            if message.isUser {
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(18)
                    
                    Text(message.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity * 0.8, alignment: .trailing)
            } else {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Image(systemName: "brain.head.profile")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        Text("AI助手")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                    
                    Text(message.content)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(Color.gray.opacity(0.1))
                        .foregroundColor(.primary)
                        .cornerRadius(18)
                    
                    Text(message.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity * 0.8, alignment: .leading)
                
                Spacer()
            }
        }
    }
    
    // MARK: - 加载指示器
    private var loadingIndicator: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 8) {
                    Image(systemName: "brain.head.profile")
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Text("AI助手")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                }
                
                HStack(spacing: 8) {
                    ProgressView()
                        .scaleEffect(0.8)
                    
                    Text("正在思考中...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(18)
            }
            .frame(maxWidth: .infinity * 0.8, alignment: .leading)
            
            Spacer()
        }
    }
    
    // MARK: - 辅助方法
    private func initializeChat() {
        // 添加初始AI消息
        let welcomeMessage = ChatMessage(
            content: "你好！我是AI助手，我已经分析了这个条款：「\(clause.title)」。你可以问我关于这个条款的任何问题，比如：\n\n• 这个条款有什么具体风险？\n• 我应该如何保护自己？\n• 类似的条款通常是什么意思？",
            isUser: false,
            timestamp: Date()
        )
        messages.append(welcomeMessage)
    }
    
    private func sendMessage() {
        let userInput = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !userInput.isEmpty else { return }
        
        // 添加用户消息
        let userMessage = ChatMessage(
            content: userInput,
            isUser: true,
            timestamp: Date()
        )
        messages.append(userMessage)
        
        // 清空输入框
        inputText = ""
        
        // 模拟AI回复
        simulateAIResponse(to: userInput)
    }
    
    private func simulateAIResponse(to userInput: String) {
        isLoading = true
        
        // 模拟网络延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            isLoading = false
            
            // 生成模拟AI回复
            let aiResponse = generateAIResponse(for: userInput)
            let aiMessage = ChatMessage(
                content: aiResponse,
                isUser: false,
                timestamp: Date()
            )
            messages.append(aiMessage)
        }
    }
    
    private func generateAIResponse(for input: String) -> String {
        // 这里应该调用真实的AI API，现在使用模拟回复
        let responses = [
            "根据我的分析，这个条款的主要风险在于...",
            "从法律角度来看，这类条款通常意味着...",
            "建议你在使用该服务时注意以下几点...",
            "这个条款与行业标准相比，存在以下特点...",
            "为了更好地保护你的权益，我建议..."
        ]
        
        return responses.randomElement() ?? "抱歉，我需要更多时间来分析这个问题。"
    }
}

#Preview {
    AIChatView(clause: TermsClause(
        title: "用户数据收集条款",
        content: "我们可能会收集您的个人信息，包括但不限于姓名、邮箱地址、设备信息等，用于改善服务质量。",
        summary: "应用会收集用户个人信息用于服务改善",
        riskLevel: .medium,
        category: .privacy,
        appName: "微信",
        appCategory: "社交",
        aiAnalysis: "该条款允许应用收集广泛的个人信息，建议用户仔细考虑隐私风险。",
        aiSummary: "中等隐私风险，需注意个人信息保护",
        keywords: ["个人信息", "数据收集", "隐私"],
        createdAt: Date(),
        updatedAt: Date()
    ))
    .environmentObject(UserState())
}
