//
//  RiskLevel.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

/// 风险等级枚举
enum RiskLevel: String, CaseIterable, Identifiable, Codable {
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var id: String { rawValue }
    
    /// 风险等级显示名称
    var displayName: String {
        switch self {
        case .high:
            return "高风险"
        case .medium:
            return "中风险"
        case .low:
            return "低风险"
        }
    }
    
    /// 风险等级对应颜色
    var color: Color {
        switch self {
        case .high:
            return .red
        case .medium:
            return .orange
        case .low:
            return .green
        }
    }
    
    /// 风险等级图标
    var icon: String {
        switch self {
        case .high:
            return "exclamationmark.triangle.fill"
        case .medium:
            return "exclamationmark.circle.fill"
        case .low:
            return "checkmark.circle.fill"
        }
    }
}
