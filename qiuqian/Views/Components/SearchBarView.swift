//
//  SearchBarView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct SearchBarView: View {
    @Binding var searchText: String
    let placeholder: String
    let onSearchChanged: ((String) -> Void)?
    
    @State private var isEditing = false
    @FocusState private var isFocused: Bool
    
    init(
        searchText: Binding<String>,
        placeholder: String = "搜索...",
        onSearchChanged: ((String) -> Void)? = nil
    ) {
        self._searchText = searchText
        self.placeholder = placeholder
        self.onSearchChanged = onSearchChanged
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // 搜索图标
            Image(systemName: "magnifyingglass")
                .foregroundColor(isEditing ? Theme.primaryBlue : Theme.textTertiary)
                .animation(Animations.easeInOut, value: isEditing)
            
            // 搜索输入框
            TextField(placeholder, text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .focused($isFocused)
                .onChange(of: searchText) { newValue in
                    onSearchChanged?(newValue)
                }
                .onTapGesture {
                    withAnimation(Animations.easeInOut) {
                        isEditing = true
                    }
                }
            
            // 清除按钮
            if !searchText.isEmpty {
                Button(action: {
                    withAnimation(Animations.easeInOut) {
                        searchText = ""
                        onSearchChanged?("")
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(Theme.textTertiary)
                }
                .transition(.scale.combined(with: .opacity))
            }
            
            // 取消按钮（编辑时显示）
            if isEditing {
                Button("取消") {
                    withAnimation(Animations.easeInOut) {
                        searchText = ""
                        isEditing = false
                        isFocused = false
                        onSearchChanged?("")
                    }
                }
                .foregroundColor(Theme.primaryBlue)
                .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Theme.backgroundSecondary)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isEditing ? Theme.primaryBlue : Color.clear, lineWidth: 1)
                )
        )
        .animation(Animations.easeInOut, value: isEditing)
        .onChange(of: isFocused) { focused in
            withAnimation(Animations.easeInOut) {
                if !focused && searchText.isEmpty {
                    isEditing = false
                }
            }
        }
    }
}

// MARK: - 搜索建议视图
struct SearchSuggestionsView: View {
    let suggestions: [String]
    let onSuggestionTapped: (String) -> Void
    
    var body: some View {
        LazyVStack(alignment: .leading, spacing: 0) {
            ForEach(suggestions, id: \.self) { suggestion in
                Button(action: {
                    onSuggestionTapped(suggestion)
                }) {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(Theme.textTertiary)
                            .font(.caption)
                        
                        Text(suggestion)
                            .foregroundColor(Theme.textPrimary)
                            .font(.subheadline)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color.clear)
                }
                .buttonStyle(PlainButtonStyle())
                
                if suggestion != suggestions.last {
                    Divider()
                        .padding(.leading, 40)
                }
            }
        }
        .background(Theme.backgroundPrimary)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

// MARK: - 搜索历史视图
struct SearchHistoryView: View {
    let history: [String]
    let onHistoryTapped: (String) -> Void
    let onClearHistory: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("搜索历史")
                    .font(.headline)
                    .foregroundColor(Theme.textPrimary)
                
                Spacer()
                
                Button("清除") {
                    onClearHistory()
                }
                .foregroundColor(Theme.primaryBlue)
                .font(.subheadline)
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(history.prefix(6), id: \.self) { item in
                    Button(action: {
                        onHistoryTapped(item)
                    }) {
                        HStack {
                            Image(systemName: "clock")
                                .font(.caption)
                                .foregroundColor(Theme.textTertiary)
                            
                            Text(item)
                                .font(.subheadline)
                                .foregroundColor(Theme.textPrimary)
                                .lineLimit(1)
                            
                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Theme.backgroundSecondary)
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        SearchBarView(
            searchText: .constant(""),
            placeholder: "搜索条款内容、应用名称..."
        )
        
        SearchSuggestionsView(
            suggestions: ["隐私条款", "数据收集", "免责声明", "自动续费"],
            onSuggestionTapped: { _ in }
        )
        
        SearchHistoryView(
            history: ["微信", "支付宝", "隐私条款", "数据收集"],
            onHistoryTapped: { _ in },
            onClearHistory: { }
        )
    }
    .padding()
}
