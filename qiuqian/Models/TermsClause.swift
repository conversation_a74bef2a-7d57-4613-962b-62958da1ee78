//
//  TermsClause.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import Foundation

/// 条款分类
enum ClauseCategory: String, CaseIterable, Identifiable, Codable {
    case privacy = "privacy"
    case liability = "liability"
    case payment = "payment"
    case usage = "usage"
    case termination = "termination"
    case other = "other"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .privacy:
            return "隐私条款"
        case .liability:
            return "免责条款"
        case .payment:
            return "费用条款"
        case .usage:
            return "使用条款"
        case .termination:
            return "终止条款"
        case .other:
            return "其他条款"
        }
    }
}

/// 条款数据模型
struct TermsClause: Identifiable, Codable {
    let id = UUID()
    let title: String
    let content: String
    let summary: String
    let riskLevel: RiskLevel
    let category: ClauseCategory
    let appName: String
    let appCategory: String
    let aiAnalysis: String
    let aiSummary: String
    let keywords: [String]
    let createdAt: Date
    let updatedAt: Date
    
    /// 是否为高风险条款
    var isHighRisk: Bool {
        riskLevel == .high
    }
    
    /// 是否为中风险条款
    var isMediumRisk: Bool {
        riskLevel == .medium
    }
    
    /// 是否为低风险条款
    var isLowRisk: Bool {
        riskLevel == .low
    }
}

/// 条款解析结果
struct TermsAnalysisResult: Identifiable, Codable {
    let id = UUID()
    let fileName: String
    let fileSize: Int
    let uploadDate: Date
    let totalClauses: Int
    let highRiskCount: Int
    let mediumRiskCount: Int
    let lowRiskCount: Int
    let clauses: [TermsClause]
    let overallRiskScore: Double
    let aiSummary: String
    
    /// 风险分布
    var riskDistribution: [RiskLevel: Int] {
        [
            .high: highRiskCount,
            .medium: mediumRiskCount,
            .low: lowRiskCount
        ]
    }
}
