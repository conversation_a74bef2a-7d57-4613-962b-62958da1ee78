// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		25D0E00E2E5B33B100B31B13 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 25D0DFF82E5B33B000B31B13 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 25D0DFFF2E5B33B000B31B13;
			remoteInfo = qiuqian;
		};
		25D0E0182E5B33B100B31B13 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 25D0DFF82E5B33B000B31B13 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 25D0DFFF2E5B33B000B31B13;
			remoteInfo = qiuqian;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		25D0E0002E5B33B000B31B13 /* qiuqian.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = qiuqian.app; sourceTree = BUILT_PRODUCTS_DIR; };
		25D0E00D2E5B33B100B31B13 /* qiuqianTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = qiuqianTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		25D0E0172E5B33B100B31B13 /* qiuqianUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = qiuqianUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		25D0E0022E5B33B000B31B13 /* qiuqian */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = qiuqian;
			sourceTree = "<group>";
		};
		25D0E0102E5B33B100B31B13 /* qiuqianTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = qiuqianTests;
			sourceTree = "<group>";
		};
		25D0E01A2E5B33B100B31B13 /* qiuqianUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = qiuqianUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		25D0DFFD2E5B33B000B31B13 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25D0E00A2E5B33B100B31B13 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25D0E0142E5B33B100B31B13 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		25D0DFF72E5B33B000B31B13 = {
			isa = PBXGroup;
			children = (
				25D0E0022E5B33B000B31B13 /* qiuqian */,
				25D0E0102E5B33B100B31B13 /* qiuqianTests */,
				25D0E01A2E5B33B100B31B13 /* qiuqianUITests */,
				25D0E0012E5B33B000B31B13 /* Products */,
			);
			sourceTree = "<group>";
		};
		25D0E0012E5B33B000B31B13 /* Products */ = {
			isa = PBXGroup;
			children = (
				25D0E0002E5B33B000B31B13 /* qiuqian.app */,
				25D0E00D2E5B33B100B31B13 /* qiuqianTests.xctest */,
				25D0E0172E5B33B100B31B13 /* qiuqianUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		25D0DFFF2E5B33B000B31B13 /* qiuqian */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 25D0E0212E5B33B100B31B13 /* Build configuration list for PBXNativeTarget "qiuqian" */;
			buildPhases = (
				25D0DFFC2E5B33B000B31B13 /* Sources */,
				25D0DFFD2E5B33B000B31B13 /* Frameworks */,
				25D0DFFE2E5B33B000B31B13 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				25D0E0022E5B33B000B31B13 /* qiuqian */,
			);
			name = qiuqian;
			packageProductDependencies = (
			);
			productName = qiuqian;
			productReference = 25D0E0002E5B33B000B31B13 /* qiuqian.app */;
			productType = "com.apple.product-type.application";
		};
		25D0E00C2E5B33B100B31B13 /* qiuqianTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 25D0E0242E5B33B100B31B13 /* Build configuration list for PBXNativeTarget "qiuqianTests" */;
			buildPhases = (
				25D0E0092E5B33B100B31B13 /* Sources */,
				25D0E00A2E5B33B100B31B13 /* Frameworks */,
				25D0E00B2E5B33B100B31B13 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				25D0E00F2E5B33B100B31B13 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				25D0E0102E5B33B100B31B13 /* qiuqianTests */,
			);
			name = qiuqianTests;
			packageProductDependencies = (
			);
			productName = qiuqianTests;
			productReference = 25D0E00D2E5B33B100B31B13 /* qiuqianTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		25D0E0162E5B33B100B31B13 /* qiuqianUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 25D0E0272E5B33B100B31B13 /* Build configuration list for PBXNativeTarget "qiuqianUITests" */;
			buildPhases = (
				25D0E0132E5B33B100B31B13 /* Sources */,
				25D0E0142E5B33B100B31B13 /* Frameworks */,
				25D0E0152E5B33B100B31B13 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				25D0E0192E5B33B100B31B13 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				25D0E01A2E5B33B100B31B13 /* qiuqianUITests */,
			);
			name = qiuqianUITests;
			packageProductDependencies = (
			);
			productName = qiuqianUITests;
			productReference = 25D0E0172E5B33B100B31B13 /* qiuqianUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		25D0DFF82E5B33B000B31B13 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					25D0DFFF2E5B33B000B31B13 = {
						CreatedOnToolsVersion = 16.4;
					};
					25D0E00C2E5B33B100B31B13 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 25D0DFFF2E5B33B000B31B13;
					};
					25D0E0162E5B33B100B31B13 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 25D0DFFF2E5B33B000B31B13;
					};
				};
			};
			buildConfigurationList = 25D0DFFB2E5B33B000B31B13 /* Build configuration list for PBXProject "qiuqian" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 25D0DFF72E5B33B000B31B13;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 25D0E0012E5B33B000B31B13 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				25D0DFFF2E5B33B000B31B13 /* qiuqian */,
				25D0E00C2E5B33B100B31B13 /* qiuqianTests */,
				25D0E0162E5B33B100B31B13 /* qiuqianUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		25D0DFFE2E5B33B000B31B13 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25D0E00B2E5B33B100B31B13 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25D0E0152E5B33B100B31B13 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		25D0DFFC2E5B33B000B31B13 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25D0E0092E5B33B100B31B13 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25D0E0132E5B33B100B31B13 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		25D0E00F2E5B33B100B31B13 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 25D0DFFF2E5B33B000B31B13 /* qiuqian */;
			targetProxy = 25D0E00E2E5B33B100B31B13 /* PBXContainerItemProxy */;
		};
		25D0E0192E5B33B100B31B13 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 25D0DFFF2E5B33B000B31B13 /* qiuqian */;
			targetProxy = 25D0E0182E5B33B100B31B13 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		25D0E01F2E5B33B100B31B13 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		25D0E0202E5B33B100B31B13 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		25D0E0222E5B33B100B31B13 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.qiuqian;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		25D0E0232E5B33B100B31B13 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.qiuqian;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		25D0E0252E5B33B100B31B13 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.qiuqianTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/qiuqian.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/qiuqian";
			};
			name = Debug;
		};
		25D0E0262E5B33B100B31B13 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.qiuqianTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/qiuqian.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/qiuqian";
			};
			name = Release;
		};
		25D0E0282E5B33B100B31B13 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.qiuqianUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = qiuqian;
			};
			name = Debug;
		};
		25D0E0292E5B33B100B31B13 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.qiuqianUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = qiuqian;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		25D0DFFB2E5B33B000B31B13 /* Build configuration list for PBXProject "qiuqian" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25D0E01F2E5B33B100B31B13 /* Debug */,
				25D0E0202E5B33B100B31B13 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		25D0E0212E5B33B100B31B13 /* Build configuration list for PBXNativeTarget "qiuqian" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25D0E0222E5B33B100B31B13 /* Debug */,
				25D0E0232E5B33B100B31B13 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		25D0E0242E5B33B100B31B13 /* Build configuration list for PBXNativeTarget "qiuqianTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25D0E0252E5B33B100B31B13 /* Debug */,
				25D0E0262E5B33B100B31B13 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		25D0E0272E5B33B100B31B13 /* Build configuration list for PBXNativeTarget "qiuqianUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25D0E0282E5B33B100B31B13 /* Debug */,
				25D0E0292E5B33B100B31B13 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 25D0DFF82E5B33B000B31B13 /* Project object */;
}
