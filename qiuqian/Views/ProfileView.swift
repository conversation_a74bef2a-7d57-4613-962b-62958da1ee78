//
//  ProfileView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var userState: UserState
    @State private var showingUpgradeSheet = false
    
    var body: some View {
        NavigationView {
            List {
                // 用户状态卡片
                userStatusSection
                
                // 功能菜单
                functionsSection
                
                // 设置菜单
                settingsSection
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("我的")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingUpgradeSheet) {
            UpgradeView()
                .environmentObject(userState)
        }
    }
    
    // MARK: - 用户状态区域
    private var userStatusSection: some View {
        Section {
            VStack(spacing: 16) {
                // 用户头像和状态
                HStack(spacing: 16) {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("用户")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        HStack {
                            Text(userState.subscriptionStatus.displayName)
                                .font(.subheadline)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(statusColor.opacity(0.2))
                                .foregroundColor(statusColor)
                                .cornerRadius(6)
                            
                            if userState.isTrialUser {
                                Text("剩余 \(userState.remainingTrialUsage) 次")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    
                    Spacer()
                }
                
                // 升级按钮（仅免费用户显示）
                if userState.isFreeUser {
                    Button("升级到付费会员") {
                        showingUpgradeSheet = true
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - 功能菜单
    private var functionsSection: some View {
        Section("功能") {
            NavigationLink(destination: Text("上传历史")) {
                menuRow(icon: "clock.fill", title: "上传历史", subtitle: "\(userState.uploadHistory.count) 个文件")
            }
            
            NavigationLink(destination: Text("收藏条款")) {
                menuRow(icon: "heart.fill", title: "收藏条款", subtitle: "\(userState.favoriteClauseIds.count) 个条款")
            }
            
            if userState.isPremiumUser {
                NavigationLink(destination: Text("导出报告")) {
                    menuRow(icon: "doc.text.fill", title: "导出报告", subtitle: "生成PDF报告")
                }
            }
        }
    }
    
    // MARK: - 设置菜单
    private var settingsSection: some View {
        Section("设置") {
            NavigationLink(destination: Text("通知设置")) {
                menuRow(icon: "bell.fill", title: "通知设置", subtitle: nil)
            }
            
            NavigationLink(destination: Text("隐私设置")) {
                menuRow(icon: "lock.fill", title: "隐私设置", subtitle: nil)
            }
            
            NavigationLink(destination: Text("帮助中心")) {
                menuRow(icon: "questionmark.circle.fill", title: "帮助中心", subtitle: nil)
            }
            
            NavigationLink(destination: Text("关于我们")) {
                menuRow(icon: "info.circle.fill", title: "关于我们", subtitle: nil)
            }
        }
    }
    
    // MARK: - 辅助方法
    private var statusColor: Color {
        switch userState.subscriptionStatus {
        case .free:
            return .gray
        case .premium:
            return .blue
        case .trial:
            return .orange
        }
    }
    
    private func menuRow(icon: String, title: String, subtitle: String?) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
    }
}

#Preview {
    ProfileView()
        .environmentObject(UserState())
}
