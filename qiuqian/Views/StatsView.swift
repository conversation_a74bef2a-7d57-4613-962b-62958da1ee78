//
//  StatsView.swift
//  qiuqian
//
//  Created by ltt on 2025/8/24.
//

import SwiftUI

struct StatsView: View {
    @EnvironmentObject var userState: UserState
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 使用统计
                    UsageStatsView()
                        .environmentObject(userState)
                    
                    // 风险分析图表
                    riskAnalysisChart
                    
                    // 最近活动
                    recentActivitySection
                }
                .padding()
            }
            .navigationTitle("统计")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    // MARK: - 风险分析图表
    private var riskAnalysisChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("风险分析趋势")
                .font(.headline)
            
            // 简单的条形图表示
            VStack(spacing: 12) {
                chartBar(title: "高风险", value: 0.3, color: .red)
                chartBar(title: "中风险", value: 0.6, color: .orange)
                chartBar(title: "低风险", value: 0.8, color: .green)
            }
            .padding()
            .background(Color(.secondarySystemBackground))
            .cornerRadius(12)
        }
    }
    
    // MARK: - 最近活动
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("最近活动")
                .font(.headline)
            
            if userState.uploadHistory.isEmpty {
                Text("暂无活动记录")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
                    .background(Color(.secondarySystemBackground))
                    .cornerRadius(12)
            } else {
                VStack(spacing: 12) {
                    ForEach(Array(userState.uploadHistory.prefix(5))) { result in
                        activityRow(result: result)
                    }
                }
            }
        }
    }
    
    // MARK: - 辅助方法
    private func chartBar(title: String, value: Double, color: Color) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .frame(width: 60, alignment: .leading)
            
            GeometryReader { geometry in
                HStack {
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * value)
                        .frame(height: 20)
                        .cornerRadius(4)
                    
                    Spacer()
                }
            }
            .frame(height: 20)
            
            Text("\(Int(value * 100))%")
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 40, alignment: .trailing)
        }
    }
    
    private func activityRow(result: TermsAnalysisResult) -> some View {
        HStack {
            Image(systemName: "doc.text.fill")
                .foregroundColor(.blue)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(result.fileName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(result.uploadDate, style: .relative)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(result.totalClauses) 条款")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 4) {
                    if result.highRiskCount > 0 {
                        Text("\(result.highRiskCount)")
                            .font(.caption2)
                            .foregroundColor(.red)
                    }
                    if result.mediumRiskCount > 0 {
                        Text("\(result.mediumRiskCount)")
                            .font(.caption2)
                            .foregroundColor(.orange)
                    }
                    if result.lowRiskCount > 0 {
                        Text("\(result.lowRiskCount)")
                            .font(.caption2)
                            .foregroundColor(.green)
                    }
                }
            }
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(8)
    }
}

#Preview {
    StatsView()
        .environmentObject(UserState())
}
